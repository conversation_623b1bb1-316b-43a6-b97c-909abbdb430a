// pages/cart/cart.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    cartItems: [],
    allSelected: false,
    selectedCount: 0,
    totalAmount: '0.00',
    showDeleteModal: false,
    deleteItemId: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadCartData();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的购物车',
      path: '/pages/cart/cart'
    };
  },

  /**
   * 加载购物车数据
   */
  loadCartData() {
    let cartItems = app.globalData.cartItems || [];

    // 如果购物车为空，添加一些测试数据用于展示
    if (cartItems.length === 0) {
      cartItems = [
        {
          id: 1,
          name: '精选有机苹果 500g装',
          price: 15.80,
          image: '/images/products/apple.jpg',
          quantity: 2,
          selected: true
        },
        {
          id: 2,
          name: '新鲜香蕉 1kg装 进口优质',
          price: 12.50,
          image: '/images/products/banana.jpg',
          quantity: 1,
          selected: true
        },
        {
          id: 3,
          name: '有机胡萝卜 300g装',
          price: 8.90,
          image: '/images/products/carrot.jpg',
          quantity: 3,
          selected: false
        }
      ];
      // 保存测试数据到全局
      app.globalData.cartItems = cartItems;
    }

    this.setData({
      cartItems: cartItems
    });
    this.calculateTotal();
  },

  /**
   * 计算总价和选中数量
   */
  calculateTotal() {
    const { cartItems } = this.data;
    let selectedCount = 0;
    let totalAmount = 0;
    let allSelected = cartItems.length > 0;

    cartItems.forEach(item => {
      if (item.selected) {
        selectedCount += item.quantity;
        totalAmount += item.price * item.quantity;
      } else {
        allSelected = false;
      }
    });

    this.setData({
      selectedCount,
      totalAmount: totalAmount.toFixed(2),
      allSelected
    });
  },

  /**
   * 切换商品选中状态
   */
  onToggleSelect(e) {
    const itemId = e.currentTarget.dataset.id;
    const { cartItems } = this.data;

    const updatedItems = cartItems.map(item => {
      if (item.id === itemId) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });

    this.setData({
      cartItems: updatedItems
    });

    // 更新全局数据
    app.globalData.cartItems = updatedItems;
    app.saveCartData();
    app.updateCartCount();

    this.calculateTotal();
  },

  /**
   * 全选/取消全选
   */
  onToggleSelectAll() {
    const { cartItems, allSelected } = this.data;
    const newSelectedState = !allSelected;

    const updatedItems = cartItems.map(item => ({
      ...item,
      selected: newSelectedState
    }));

    this.setData({
      cartItems: updatedItems,
      allSelected: newSelectedState
    });

    // 更新全局数据
    app.globalData.cartItems = updatedItems;
    app.saveCartData();
    app.updateCartCount();

    this.calculateTotal();
  },

  /**
   * 数量变更
   */
  onQuantityChange(e) {
    const { id, type } = e.currentTarget.dataset;
    const { cartItems } = this.data;

    const updatedItems = cartItems.map(item => {
      if (item.id === id) {
        let newQuantity = item.quantity;
        if (type === 'increase') {
          newQuantity += 1;
        } else if (type === 'decrease' && newQuantity > 1) {
          newQuantity -= 1;
        }
        return { ...item, quantity: newQuantity };
      }
      return item;
    });

    this.setData({
      cartItems: updatedItems
    });

    // 更新全局数据
    app.globalData.cartItems = updatedItems;
    app.saveCartData();
    app.updateCartCount();

    this.calculateTotal();
  },

  /**
   * 数量输入
   */
  onQuantityInput(e) {
    const itemId = e.currentTarget.dataset.id;
    const newQuantity = parseInt(e.detail.value) || 1;
    const { cartItems } = this.data;

    const updatedItems = cartItems.map(item => {
      if (item.id === itemId) {
        return { ...item, quantity: Math.max(1, newQuantity) };
      }
      return item;
    });

    this.setData({
      cartItems: updatedItems
    });

    // 更新全局数据
    app.globalData.cartItems = updatedItems;
    app.saveCartData();
    app.updateCartCount();

    this.calculateTotal();
  },

  /**
   * 删除商品
   */
  onDeleteItem(e) {
    const itemId = e.currentTarget.dataset.id;
    this.setData({
      showDeleteModal: true,
      deleteItemId: itemId
    });
  },

  /**
   * 确认删除
   */
  onConfirmDelete() {
    const { deleteItemId, cartItems } = this.data;
    const updatedItems = cartItems.filter(item => item.id !== deleteItemId);

    this.setData({
      cartItems: updatedItems,
      showDeleteModal: false,
      deleteItemId: null
    });

    // 更新全局数据
    app.globalData.cartItems = updatedItems;
    app.saveCartData();
    app.updateCartCount();

    this.calculateTotal();

    wx.showToast({
      title: '删除成功',
      icon: 'success'
    });
  },

  /**
   * 取消删除
   */
  onCancelDelete() {
    this.setData({
      showDeleteModal: false,
      deleteItemId: null
    });
  },

  /**
   * 去购物
   */
  onGoShopping() {
    wx.switchTab({
      url: '/pages/category/category'
    });
  },

  /**
   * 结算
   */
  onCheckout() {
    const { selectedCount, totalAmount } = this.data;

    if (selectedCount === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认结算',
      content: `共${selectedCount}件商品，合计¥${totalAmount}`,
      confirmText: '去结算',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '跳转到结算页面',
            icon: 'none'
          });
          // 这里可以跳转到结算页面
        }
      }
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
})