{% extends "base.html" %}

{% block title %}内容管理 - 漆器文化商城管理后台{% endblock %}

{% block page_title %}内容管理{% endblock %}

{% block content %}
<!-- 导航标签 -->
<ul class="nav nav-tabs mb-4" id="contentTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button" role="tab">
            <i class="fas fa-home me-2"></i>首页内容
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="category-tab" data-bs-toggle="tab" data-bs-target="#category" type="button" role="tab">
            <i class="fas fa-th-large me-2"></i>商品分类
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="user-tab" data-bs-toggle="tab" data-bs-target="#user" type="button" role="tab">
            <i class="fas fa-user me-2"></i>用户信息
        </button>
    </li>
</ul>

<div class="tab-content" id="contentTabsContent">
    <!-- 首页内容管理 -->
    <div class="tab-pane fade show active" id="home" role="tabpanel">
        <form id="homeForm">
            <!-- 指南轮播 -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">漆器收藏与鉴赏指南轮播</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-warning" onclick="fixImagePaths()" title="修复图片和视频路径，将本地路径转换为网络地址">
                            <i class="fas fa-wrench"></i> 修复路径
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" onclick="addGuideItem()">
                            <i class="fas fa-plus"></i> 添加指南
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="guideItems">
                        {% for guide in data.home_data.guide_data %}
                        <div class="guide-item border rounded p-3 mb-3" data-index="{{ loop.index0 }}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">指南 {{ loop.index }}</h6>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeGuideItem({{ loop.index0 }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">指南标题</label>
                                        <input type="text" class="form-control" name="guide_title_{{ loop.index0 }}"
                                               value="{{ guide.title }}">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">专家名称</label>
                                        <input type="text" class="form-control" name="guide_expert_{{ loop.index0 }}"
                                               value="{{ guide.expert_name }}">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">发布日期</label>
                                        <input type="text" class="form-control" name="guide_date_{{ loop.index0 }}"
                                               value="{{ guide.publish_date }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">封面图片</label>
                                        <input type="text" class="form-control" name="guide_image_{{ loop.index0 }}"
                                               value="{{ guide.cover_image }}">
                                        <small class="form-text text-muted">图片路径，如：/images/guide-cover.jpg 或 http://127.0.0.1:5000/images/guide-cover.jpg</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">描述</label>
                                        <textarea class="form-control" name="guide_description_{{ loop.index0 }}" rows="2">{{ guide.description }}</textarea>
                                    </div>
                                    <div class="image-preview-container">
                                        <img src="{{ guide.cover_image }}"
                                             class="image-preview" alt="封面预览"
                                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCAyNUM1Ni42Mjc0IDI1IDYyIDMwLjM3MjYgNjIgMzdDNjIgNDMuNjI3NCA1Ni42Mjc0IDQ5IDUwIDQ5QzQzLjM3MjYgNDkgMzggNDMuNjI3NCAzOCAzN0MzOCAzMC4zNzI2IDQzLjM3MjYgMjUgNTAgMjVaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik0yMCA3NUg4MEw3MCA2MEgzMEwyMCA3NVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'">
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- 快捷入口 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">快捷入口按钮</h5>
                </div>
                <div class="card-body">
                    <div id="quickActionsContainer">
                        {% for action in data.home_data.quick_actions %}
                        <div class="row mb-3 quick-action-item">
                            <div class="col-md-3">
                                <label class="form-label">图标</label>
                                <input type="text" class="form-control" name="action_icon_{{ action.id }}" 
                                       value="{{ action.icon }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">文字</label>
                                <input type="text" class="form-control" name="action_text_{{ action.id }}" 
                                       value="{{ action.text }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">动作</label>
                                <input type="text" class="form-control" name="action_action_{{ action.id }}" 
                                       value="{{ action.action }}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" class="btn btn-danger btn-sm" onclick="removeQuickAction(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addQuickAction()">
                        <i class="fas fa-plus me-1"></i>添加快捷入口
                    </button>
                </div>
            </div>

            <!-- 视频列表 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">视频轮播内容</h5>
                </div>
                <div class="card-body">
                    <div id="videoListContainer">
                        {% for video in data.home_data.video_list %}
                        <div class="row mb-3 video-item">
                            <div class="col-md-3">
                                <label class="form-label">视频标题</label>
                                <input type="text" class="form-control" name="video_title_{{ video.id }}"
                                       value="{{ video.title }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">视频URL</label>
                                <input type="text" class="form-control" name="video_url_{{ video.id }}"
                                       value="{{ video.url }}" placeholder="/videos/video.mp4">
                                <small class="form-text text-muted">视频文件路径</small>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">缩略图</label>
                                <input type="text" class="form-control" name="video_thumbnail_{{ video.id }}"
                                       value="{{ video.thumbnail }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">时长</label>
                                <input type="text" class="form-control" name="video_duration_{{ video.id }}"
                                       value="{{ video.duration }}">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">预览</label>
                                <img src="{{ video.thumbnail }}" class="image-preview" alt="视频缩略图"
                                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA0MFY2MEw2MCA1MEw0MCA0MFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" class="btn btn-danger btn-sm" onclick="removeVideo(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addVideo()">
                        <i class="fas fa-plus me-1"></i>添加视频
                    </button>
                </div>
            </div>

            <!-- 项目历程 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">项目历程时间线</h5>
                </div>
                <div class="card-body">
                    <div id="timelineContainer">
                        {% for item in data.home_data.timeline_data %}
                        <div class="row mb-3 timeline-item">
                            <div class="col-md-2">
                                <label class="form-label">年份</label>
                                <input type="text" class="form-control" name="timeline_year_{{ item.id }}" 
                                       value="{{ item.year }}">
                            </div>
                            <div class="col-md-9">
                                <label class="form-label">描述</label>
                                <input type="text" class="form-control" name="timeline_desc_{{ item.id }}" 
                                       value="{{ item.description }}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" class="btn btn-danger btn-sm" onclick="removeTimeline(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addTimeline()">
                        <i class="fas fa-plus me-1"></i>添加历程节点
                    </button>
                </div>
            </div>

            <div class="text-end">
                <button type="button" class="btn btn-primary" onclick="saveHomeContent()">
                    <i class="fas fa-save me-1"></i>保存首页内容
                </button>
            </div>
        </form>
    </div>

    <!-- 商品分类管理 -->
    <div class="tab-pane fade" id="category" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">商品分类管理</h5>
            </div>
            <div class="card-body">
                <form id="categoryForm">
                    <div id="categoryContainer">
                        {% for category in data.category_data.categories %}
                        <div class="row mb-3 category-item">
                            <div class="col-md-4">
                                <label class="form-label">分类名称</label>
                                <input type="text" class="form-control" name="category_name_{{ category.id }}" 
                                       value="{{ category.name }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">商品数量</label>
                                <input type="number" class="form-control" name="category_count_{{ category.id }}" 
                                       value="{{ category.count }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">分类标识</label>
                                <input type="text" class="form-control" name="category_key_{{ category.id }}" 
                                       value="{{ category.category }}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" class="btn btn-danger btn-sm" onclick="removeCategory(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm mb-3" onclick="addCategory()">
                        <i class="fas fa-plus me-1"></i>添加分类
                    </button>
                    <div class="text-end">
                        <button type="button" class="btn btn-primary" onclick="saveCategoryContent()">
                            <i class="fas fa-save me-1"></i>保存分类设置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 用户信息管理 -->
    <div class="tab-pane fade" id="user" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">默认用户信息</h5>
            </div>
            <div class="card-body">
                <form id="userForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">用户昵称</label>
                                <input type="text" class="form-control" name="user_nickname" 
                                       value="{{ data.user_data.default_user.nick_name }}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">手机号</label>
                                <input type="text" class="form-control" name="user_phone" 
                                       value="{{ data.user_data.default_user.phone }}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">会员等级</label>
                                <select class="form-control" name="user_level">
                                    <option value="普通" {% if data.user_data.default_user.level == '普通' %}selected{% endif %}>普通</option>
                                    <option value="VIP" {% if data.user_data.default_user.level == 'VIP' %}selected{% endif %}>VIP</option>
                                    <option value="SVIP" {% if data.user_data.default_user.level == 'SVIP' %}selected{% endif %}>SVIP</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">积分</label>
                                <input type="number" class="form-control" name="user_points" 
                                       value="{{ data.user_data.default_user.points }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">头像路径</label>
                                <input type="text" class="form-control" name="user_avatar" 
                                       value="{{ data.user_data.default_user.avatar_url }}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">头像预览</label>
                                <div>
                                    <img src="{{ data.user_data.default_user.avatar_url }}" 
                                         class="image-preview" alt="头像预览"
                                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjE1IiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik0yMCA4MEM0MCA2MCA2MCA2MCA4MCA4MEgyMFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'">
                                </div>
                            </div>
                            
                            <h6 class="mt-4">资产设置</h6>
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">优惠券</label>
                                        <input type="number" class="form-control" name="user_coupons" 
                                               value="{{ data.user_data.assets.coupons }}">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <label class="form-label">卡券</label>
                                        <input type="number" class="form-control" name="user_cards" 
                                               value="{{ data.user_data.assets.cards }}">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">现金余额</label>
                                <input type="text" class="form-control" name="user_balance" 
                                       value="{{ data.user_data.assets.balance }}">
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-primary" onclick="saveUserContent()">
                            <i class="fas fa-save me-1"></i>保存用户设置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let quickActionCounter = {{ data.home_data.quick_actions|length }};
    let videoCounter = {{ data.home_data.video_list|length }};
    let timelineCounter = {{ data.home_data.timeline_data|length }};
    let categoryCounter = {{ data.category_data.categories|length }};

    // 添加快捷入口
    function addQuickAction() {
        quickActionCounter++;
        const container = document.getElementById('quickActionsContainer');
        const div = document.createElement('div');
        div.className = 'row mb-3 quick-action-item';
        div.innerHTML = `
            <div class="col-md-3">
                <label class="form-label">图标</label>
                <input type="text" class="form-control" name="action_icon_${quickActionCounter}" value="🎯">
            </div>
            <div class="col-md-4">
                <label class="form-label">文字</label>
                <input type="text" class="form-control" name="action_text_${quickActionCounter}" value="新功能">
            </div>
            <div class="col-md-4">
                <label class="form-label">动作</label>
                <input type="text" class="form-control" name="action_action_${quickActionCounter}" value="new_action">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeQuickAction(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(div);
    }

    // 删除快捷入口
    function removeQuickAction(button) {
        if (confirm('确定要删除这个快捷入口吗？')) {
            button.closest('.quick-action-item').remove();
        }
    }

    // 添加视频
    function addVideo() {
        videoCounter++;
        const container = document.getElementById('videoListContainer');
        const div = document.createElement('div');
        div.className = 'row mb-3 video-item';
        div.innerHTML = `
            <div class="col-md-3">
                <label class="form-label">视频标题</label>
                <input type="text" class="form-control" name="video_title_${videoCounter}" value="新视频">
            </div>
            <div class="col-md-3">
                <label class="form-label">视频URL</label>
                <input type="text" class="form-control" name="video_url_${videoCounter}" value="/videos/video.mp4" placeholder="/videos/video.mp4">
                <small class="form-text text-muted">视频文件路径</small>
            </div>
            <div class="col-md-2">
                <label class="form-label">缩略图</label>
                <input type="text" class="form-control" name="video_thumbnail_${videoCounter}" value="/images/video.jpg">
            </div>
            <div class="col-md-2">
                <label class="form-label">时长</label>
                <input type="text" class="form-control" name="video_duration_${videoCounter}" value="00:00">
            </div>
            <div class="col-md-1">
                <label class="form-label">预览</label>
                <img src="/images/video.jpg" class="image-preview" alt="视频缩略图">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeVideo(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(div);
    }

    // 删除视频
    function removeVideo(button) {
        if (confirm('确定要删除这个视频吗？')) {
            button.closest('.video-item').remove();
        }
    }

    // 添加时间线节点
    function addTimeline() {
        timelineCounter++;
        const container = document.getElementById('timelineContainer');
        const div = document.createElement('div');
        div.className = 'row mb-3 timeline-item';
        div.innerHTML = `
            <div class="col-md-2">
                <label class="form-label">年份</label>
                <input type="text" class="form-control" name="timeline_year_${timelineCounter}" value="2024年">
            </div>
            <div class="col-md-9">
                <label class="form-label">描述</label>
                <input type="text" class="form-control" name="timeline_desc_${timelineCounter}" value="新的里程碑">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeTimeline(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(div);
    }

    // 删除时间线节点
    function removeTimeline(button) {
        if (confirm('确定要删除这个历程节点吗？')) {
            button.closest('.timeline-item').remove();
        }
    }

    // 添加分类
    function addCategory() {
        categoryCounter++;
        const container = document.getElementById('categoryContainer');
        const div = document.createElement('div');
        div.className = 'row mb-3 category-item';
        div.innerHTML = `
            <div class="col-md-4">
                <label class="form-label">分类名称</label>
                <input type="text" class="form-control" name="category_name_${categoryCounter}" value="新分类">
            </div>
            <div class="col-md-3">
                <label class="form-label">商品数量</label>
                <input type="number" class="form-control" name="category_count_${categoryCounter}" value="0">
            </div>
            <div class="col-md-4">
                <label class="form-label">分类标识</label>
                <input type="text" class="form-control" name="category_key_${categoryCounter}" value="new_category">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeCategory(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(div);
    }

    // 删除分类
    function removeCategory(button) {
        if (confirm('确定要删除这个分类吗？')) {
            button.closest('.category-item').remove();
        }
    }

    // 保存首页内容
    function saveHomeContent() {
        const formData = new FormData(document.getElementById('homeForm'));
        const data = {};

        // 收集表单数据
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // 发送保存请求
        fetch('/api/save_home_content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                let message = result.message;
                if (result.refresh_required) {
                    message += '\n小程序数据将在下次访问时自动更新';
                }
                showSuccess(message);

                // 如果需要刷新，可以在这里添加通知小程序的逻辑
                if (result.refresh_required) {
                    console.log('数据已更新，小程序将在下次访问时获取最新数据');
                }
            } else {
                showError('保存失败：' + result.message);
            }
        })
        .catch(error => {
            showError('保存失败：' + error);
        });
    }

    // 保存分类内容
    function saveCategoryContent() {
        const formData = new FormData(document.getElementById('categoryForm'));
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        fetch('/api/save_category_content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess('分类设置保存成功！');
            } else {
                showError('保存失败：' + result.message);
            }
        })
        .catch(error => {
            showError('保存失败：' + error);
        });
    }

    // 保存用户内容
    function saveUserContent() {
        const formData = new FormData(document.getElementById('userForm'));
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        fetch('/api/save_user_content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess('用户设置保存成功！');
            } else {
                showError('保存失败：' + result.message);
            }
        })
        .catch(error => {
            showError('保存失败：' + error);
        });
    }

    // 添加指南项
    function addGuideItem() {
        const container = document.getElementById('guideItems');
        const index = container.children.length;

        const guideItem = document.createElement('div');
        guideItem.className = 'guide-item border rounded p-3 mb-3';
        guideItem.setAttribute('data-index', index);

        guideItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start mb-2">
                <h6 class="mb-0">指南 ${index + 1}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeGuideItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">指南标题</label>
                        <input type="text" class="form-control" name="guide_title_${index}" value="">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">专家名称</label>
                        <input type="text" class="form-control" name="guide_expert_${index}" value="">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">发布日期</label>
                        <input type="text" class="form-control" name="guide_date_${index}" value="">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">封面图片</label>
                        <input type="text" class="form-control" name="guide_image_${index}" value="">
                        <small class="form-text text-muted">图片路径，如：/images/guide-cover.jpg</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="guide_description_${index}" rows="2"></textarea>
                    </div>
                    <div class="image-preview-container">
                        <img src="" class="image-preview" alt="封面预览"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCAyNUM1Ni42Mjc0IDI1IDYyIDMwLjM3MjYgNjIgMzdDNjIgNDMuNjI3NCA1Ni42Mjc0IDQ5IDUwIDQ5QzQzLjM3MjYgNDkgMzggNDMuNjI3NCAzOCAzN0MzOCAzMC4zNzI2IDQzLjM3MjYgMjUgNTAgMjVaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik0yMCA3NUg4MEw3MCA2MEgzMEwyMCA3NVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'">
                    </div>
                </div>
            </div>
        `;

        container.appendChild(guideItem);
    }

    // 删除指南项
    function removeGuideItem(index) {
        const container = document.getElementById('guideItems');
        const item = container.querySelector(`[data-index="${index}"]`);
        if (item) {
            item.remove();
            // 重新编号
            updateGuideIndexes();
        }
    }

    // 更新指南项索引
    function updateGuideIndexes() {
        const container = document.getElementById('guideItems');
        const items = container.children;

        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            item.setAttribute('data-index', i);

            // 更新标题
            const title = item.querySelector('h6');
            title.textContent = `指南 ${i + 1}`;

            // 更新删除按钮
            const deleteBtn = item.querySelector('.btn-outline-danger');
            deleteBtn.setAttribute('onclick', `removeGuideItem(${i})`);

            // 更新表单字段名称
            const inputs = item.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                const name = input.name;
                if (name) {
                    const baseName = name.replace(/_\d+$/, '');
                    input.name = `${baseName}_${i}`;
                }
            });
        }
    }

    // 修复图片和视频路径
    function fixImagePaths() {
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;

        // 显示加载状态
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 修复中...';
        btn.disabled = true;

        fetch('/api/fix_image_paths', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess(result.message);
                // 刷新页面以显示修复后的路径
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showError('修复失败：' + result.message);
            }
        })
        .catch(error => {
            showError('路径修复失败：' + error);
        })
        .finally(() => {
            // 恢复按钮状态
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }

    // 图片预览更新
    document.addEventListener('input', function(e) {
        if (e.target.name && (e.target.name.includes('image') || e.target.name.includes('thumbnail') || e.target.name.includes('avatar'))) {
            const preview = e.target.closest('.row').querySelector('.image-preview');
            if (preview) {
                preview.src = e.target.value;
            }
        }
    });
</script>
{% endblock %}
