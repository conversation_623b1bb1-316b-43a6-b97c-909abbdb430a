<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>视频播放测试</h1>
        <p class="text-muted">测试视频文件是否可以正常播放</p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>在线测试视频</h3>
                <div class="mb-3">
                    <h5>BigBuckBunny (测试视频1)</h5>
                    <video controls width="100%" height="200">
                        <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                </div>
                
                <div class="mb-3">
                    <h5>ElephantsDream (测试视频2)</h5>
                    <video controls width="100%" height="200">
                        <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>本地视频文件</h3>
                <div class="mb-3">
                    <h5>craft_demo.mp4</h5>
                    <video controls width="100%" height="200">
                        <source src="/videos/craft_demo.mp4" type="video/mp4">
                        视频文件不存在或格式不支持
                    </video>
                    <small class="text-muted">如果无法播放，请检查 videos/ 目录下是否有此文件</small>
                </div>
                
                <div class="mb-3">
                    <h5>culture_intro.mp4</h5>
                    <video controls width="100%" height="200">
                        <source src="/videos/culture_intro.mp4" type="video/mp4">
                        视频文件不存在或格式不支持
                    </video>
                    <small class="text-muted">如果无法播放，请检查 videos/ 目录下是否有此文件</small>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>当前小程序视频配置</h3>
            <div class="card">
                <div class="card-body">
                    <button class="btn btn-primary mb-3" onclick="loadVideoConfig()">
                        <i class="fas fa-sync-alt"></i> 加载当前配置
                    </button>
                    <div id="videoConfigDisplay">
                        <p class="text-muted">点击上方按钮加载当前小程序的视频配置</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3>测试说明</h3>
            <div class="alert alert-info">
                <h5>如果在线视频可以播放：</h5>
                <ul>
                    <li>说明网络连接正常</li>
                    <li>浏览器支持视频播放</li>
                    <li>小程序中的视频播放问题可能是域名配置或格式问题</li>
                </ul>

                <h5>如果本地视频可以播放：</h5>
                <ul>
                    <li>说明静态文件服务配置正确</li>
                    <li>视频文件格式正确</li>
                    <li>可以在小程序中使用本地视频</li>
                </ul>

                <h5>如果都无法播放：</h5>
                <ul>
                    <li>检查网络连接</li>
                    <li>检查浏览器是否支持HTML5视频</li>
                    <li>检查防火墙设置</li>
                </ul>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="/videos" class="btn btn-primary">返回视频管理</a>
            <a href="/" class="btn btn-secondary">返回首页</a>
        </div>
    </div>

    <script>
        // 加载视频配置
        function loadVideoConfig() {
            fetch('/api/miniprogram/home')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.video_list) {
                        const videoList = data.data.video_list;
                        let html = '<h5>当前配置的视频 (' + videoList.length + ' 个):</h5>';

                        if (videoList.length === 0) {
                            html += '<p class="text-warning">没有配置任何视频</p>';
                        } else {
                            html += '<div class="table-responsive"><table class="table table-sm">';
                            html += '<thead><tr><th>ID</th><th>标题</th><th>URL</th><th>缩略图</th><th>状态</th></tr></thead><tbody>';

                            videoList.forEach(video => {
                                const isLocal = video.url.includes('127.0.0.1') || video.url.startsWith('/videos/');
                                const statusClass = isLocal ? 'badge bg-primary' : 'badge bg-success';
                                const statusText = isLocal ? '本地文件' : '在线视频';

                                html += `<tr>
                                    <td>${video.id}</td>
                                    <td>${video.title}</td>
                                    <td><small>${video.url}</small></td>
                                    <td><small>${video.thumbnail}</small></td>
                                    <td><span class="${statusClass}">${statusText}</span></td>
                                </tr>`;
                            });

                            html += '</tbody></table></div>';
                        }

                        html += '<div class="mt-3">';
                        html += '<button class="btn btn-sm btn-success" onclick="refreshVideoConfig()">刷新视频配置</button>';
                        html += '</div>';

                        document.getElementById('videoConfigDisplay').innerHTML = html;
                    } else {
                        document.getElementById('videoConfigDisplay').innerHTML =
                            '<div class="alert alert-danger">加载配置失败: ' + (data.message || '未知错误') + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('videoConfigDisplay').innerHTML =
                        '<div class="alert alert-danger">加载配置失败: ' + error + '</div>';
                });
        }

        // 刷新视频配置
        function refreshVideoConfig() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
            btn.disabled = true;

            fetch('/api/refresh_videos', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('视频配置已刷新！');
                    loadVideoConfig(); // 重新加载配置
                } else {
                    alert('刷新失败：' + result.message);
                }
            })
            .catch(error => {
                alert('刷新失败：' + error);
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }
    </script>
</body>
</html>
