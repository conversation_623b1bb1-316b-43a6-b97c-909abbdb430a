<!-- pages/home/<USER>
<view class="home-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">非物质文化遗产 · 漆器</text>
  </view>

  <!-- 漆器收藏与鉴赏指南轮播 -->
  <view class="guide-section">
    <swiper
      class="guide-swiper"
      indicator-dots="{{guideData.length > 1}}"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#fff"
      autoplay="{{true}}"
      interval="4000"
      duration="500"
      circular="{{guideData.length > 1}}"
    >
      <swiper-item
        wx:for="{{guideData}}"
        wx:key="id"
        class="guide-slide"
      >
        <view class="guide-card card" bindtap="onGuideCardTap" data-guide="{{item}}">
          <image class="guide-image" src="{{item.cover_image}}" mode="aspectFill" lazy-load="{{true}}" />
          <view class="guide-content">
            <text class="guide-title">{{item.title}}</text>
            <text class="guide-description">{{item.description}}</text>
            <view class="guide-meta">
              <text class="expert-name">{{item.expert_name}}</text>
              <text class="publish-date">{{item.publish_date}}</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快捷入口 -->
  <view class="quick-actions">
    <view
      class="action-card card"
      wx:for="{{quickActions}}"
      wx:key="id"
      bindtap="onQuickActionTap"
      data-action="{{item.action}}"
    >
      <text class="action-icon">{{item.icon}}</text>
      <text class="action-text">{{item.text}}</text>
    </view>
  </view>

  <!-- 视频轮播区块 -->
  <view class="video-section">
    <text class="section-title">工艺传承</text>
    <swiper
      class="video-swiper"
      indicator-dots="{{videoList.length > 1}}"
      indicator-color="rgba(0, 0, 0, .3)"
      indicator-active-color="{{primaryColor}}"
      autoplay="{{false}}"
      interval="5000"
      duration="500"
      circular="{{videoList.length > 1}}"
      display-multiple-items="{{videoList.length >= 2 ? 1.2 : 1}}"
      next-margin="{{videoList.length >= 2 ? '32rpx' : '0rpx'}}"
    >
      <swiper-item
        wx:for="{{videoList}}"
        wx:key="id"
        class="video-slide"
      >
        <view class="video-card card">
          <view class="video-container">
            <!-- 视频播放器 -->
            <video
              wx:if="{{item.showPlayer}}"
              id="video-{{item.id}}"
              src="{{item.url}}"
              poster="{{item.thumbnail}}"
              controls="{{true}}"
              autoplay="{{false}}"
              loop="{{false}}"
              muted="{{false}}"
              show-center-play-btn="{{true}}"
              show-play-btn="{{true}}"
              show-fullscreen-btn="{{true}}"
              show-progress="{{true}}"
              enable-progress-gesture="{{true}}"
              object-fit="contain"
              bindplay="onVideoPlay"
              bindpause="onVideoPause"
              bindended="onVideoEnded"
              binderror="onVideoError"
              data-video="{{item}}"
            ></video>

            <!-- 视频缩略图 -->
            <view wx:else class="video-thumbnail" bindtap="onVideoTap" data-video="{{item}}">
              <image src="{{item.thumbnail}}" mode="aspectFill" lazy-load="{{true}}" />
              <view class="play-button">
                <text class="play-icon">▶</text>
              </view>
              <view class="video-duration-badge">{{item.duration}}</view>
            </view>
          </view>
          <view class="video-info">
            <text class="video-title">{{item.title}}</text>
            <view class="video-actions">
              <text class="video-duration">{{item.duration}}</text>
              <view class="video-controls">
                <text wx:if="{{!item.showPlayer}}" class="control-btn" bindtap="onVideoTap" data-video="{{item}}">播放</text>
                <text wx:else class="control-btn" bindtap="onVideoClose" data-video="{{item}}">收起</text>
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 项目历程时间线 -->
  <view class="timeline-section">
    <text class="section-title">项目历程</text>
    <view class="timeline">
      <view
        class="timeline-item"
        wx:for="{{timelineData}}"
        wx:key="id"
        wx:for-index="index"
      >
        <view class="timeline-dot {{index === timelineData.length - 1 ? 'active' : ''}}"></view>
        <view class="timeline-content card">
          <text class="timeline-year">{{item.year}}</text>
          <text class="timeline-desc">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 推荐商品区块 -->
  <view class="recommend-section" wx:if="{{recommendProducts.length > 0}}">
    <view class="section-header">
      <text class="section-title">精品推荐</text>
      <text class="view-more" bindtap="onViewMoreProducts">查看更多 →</text>
    </view>
    <scroll-view class="recommend-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="recommend-list">
        <view
          class="product-card card"
          wx:for="{{recommendProducts}}"
          wx:key="id"
          bindtap="onProductTap"
          data-product="{{item}}"
        >
          <image class="product-image" src="{{item.image}}" mode="aspectFill" lazy-load="{{true}}" />
          <view class="product-info">
            <text class="product-name">{{item.name}}</text>
            <text class="product-price">¥{{item.price}}</text>
          </view>
          <view class="add-cart-btn" bindtap="onAddToCart" data-product="{{item}}" catchtap="true">
            <text class="add-icon">+</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 文化介绍区块 -->
  <view class="culture-section">
    <text class="section-title">漆器文化</text>
    <swiper
      class="culture-swiper"
      indicator-dots="{{cultureArticles.length > 1}}"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#fff"
      autoplay="{{cultureArticles.length > 1}}"
      interval="5000"
      duration="500"
      circular="{{cultureArticles.length > 1}}"
    >
      <swiper-item
        wx:for="{{cultureArticles}}"
        wx:key="id"
        class="culture-slide"
      >
        <view class="culture-card card" bindtap="onCultureTap" data-article="{{item}}">
          <image class="culture-image" src="{{item.cover_image}}" mode="aspectFill" lazy-load="{{true}}" />
          <view class="culture-overlay">
            <text class="culture-title">{{item.title}}</text>
            <text class="culture-desc">{{item.subtitle || item.summary}}</text>
            <view class="culture-meta">
              <text class="culture-author">{{item.author}}</text>
              <text class="culture-date">{{item.publish_date}}</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">加载中...</text>
</view>