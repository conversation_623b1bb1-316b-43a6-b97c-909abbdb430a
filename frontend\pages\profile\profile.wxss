/* pages/profile/profile.wxss */

/* 用户中心页面容器 */
.profile-container {
  padding: var(--spacing-md);
  background-color: var(--cream-white);
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-dark);
}

/* 用户信息卡片 */
.user-card {
  padding: 32rpx;
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: 24rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 140, 66, 0.1);
  position: relative;
  overflow: hidden;
  min-height: 160rpx; /* 确保卡片有足够高度 */
}

.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-orange) 0%, #ff7a2e 100%);
}

.user-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(255, 140, 66, 0.2);
  border: 4rpx solid rgba(255, 140, 66, 0.1);
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 140, 66, 0.1) 100%);
  z-index: 1;
}

.user-avatar image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.user-card:active .user-avatar image {
  transform: scale(1.05);
}

.user-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  font-size: 40rpx;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 8rpx;
  display: block;
  background: linear-gradient(135deg, var(--text-dark) 0%, #666 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.user-phone {
  font-size: 28rpx;
  color: var(--text-gray);
  margin-bottom: 16rpx;
  display: block;
}

.user-level {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap; /* 允许换行，避免挤压 */
  margin-top: 8rpx;
}

.level-text {
  font-size: 24rpx;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--primary-orange) 0%, #ff7a2e 100%);
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 66, 0.3);
  white-space: nowrap; /* 防止文字换行 */
  flex-shrink: 0;
}

.points-text {
  font-size: 24rpx;
  color: var(--text-gray);
  font-weight: 500;
  white-space: nowrap; /* 防止文字换行 */
  flex-shrink: 0;
}

.edit-btn {
  background: linear-gradient(135deg, var(--primary-orange) 0%, #ff7a2e 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx; /* 增加内边距，让按钮更大 */
  font-size: 26rpx; /* 稍微减小字体，但增加按钮尺寸 */
  font-weight: 500; /* 修正字重值 */
  flex-shrink: 0;
  box-shadow: 0 6rpx 12rpx rgba(255, 140, 66, 0.3);
  transition: all 0.3s ease;
  min-width: 80rpx; /* 确保按钮有最小宽度 */
  height: 60rpx; /* 固定按钮高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 12rpx rgba(255, 140, 66, 0.4);
}

.edit-btn::after {
  border: none;
}

/* 资产概览 */
.assets-overview {
  padding: 32rpx;
  margin-bottom: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 140, 66, 0.1);
  position: relative;
  overflow: hidden;
}

.assets-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-orange) 0%, #ff7a2e 50%, #ffb366 100%);
}

.asset-item {
  text-align: center;
  padding: 20rpx 12rpx;
  transition: all 0.3s ease;
  border-radius: 16rpx;
  position: relative;
}

.asset-item:active {
  transform: translateY(-4rpx);
  background: linear-gradient(135deg, rgba(255, 140, 66, 0.05) 0%, rgba(255, 140, 66, 0.02) 100%);
}

.asset-value {
  font-size: 40rpx;
  font-weight: 700;
  color: var(--primary-orange);
  margin-bottom: 8rpx;
  display: block;
  background: linear-gradient(135deg, var(--primary-orange) 0%, #ff7a2e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.asset-label {
  font-size: 26rpx;
  color: var(--text-gray);
  font-weight: 500;
}

/* 订单部分 */
.order-section {
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
}

.view-all {
  font-size: 28rpx;
  color: var(--primary-orange);
}

.order-status-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--spacing-sm);
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  transition: all 0.3s ease;
}

.order-status-item:active {
  transform: scale(0.95);
  background: var(--cream-white);
  border-radius: var(--radius-small);
}

.status-icon-wrapper {
  position: relative;
  margin-bottom: var(--spacing-xs);
}

.status-icon {
  font-size: 48rpx;
}

.status-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.status-text {
  font-size: 24rpx;
  color: var(--text-gray);
  text-align: center;
}

/* 功能卡片 */
.function-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.function-card {
  padding: var(--spacing-lg) var(--spacing-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.function-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 12rpx var(--shadow-light);
}

.function-icon {
  font-size: 56rpx;
  margin-bottom: var(--spacing-sm);
}

.function-text {
  font-size: 28rpx;
  color: var(--text-dark);
  text-align: center;
}

/* 独立功能入口 */
.standalone-functions {
  padding: 0;
  overflow: hidden;
}

.function-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-md);
  border-bottom: 2rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:active {
  background: var(--cream-white);
}

.function-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.function-left .function-icon {
  font-size: 40rpx;
}

.function-name {
  font-size: 32rpx;
  color: var(--text-dark);
}

.function-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.function-count {
  font-size: 28rpx;
  color: var(--text-gray);
}

.function-arrow {
  font-size: 32rpx;
  color: var(--primary-orange);
  font-weight: bold;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .user-card {
    padding: 24rpx;
    gap: 16rpx;
  }

  .user-avatar {
    width: 120rpx;
    height: 120rpx;
  }

  .user-name {
    font-size: 36rpx;
  }

  .edit-btn {
    padding: 12rpx 18rpx;
    font-size: 24rpx;
    min-width: 70rpx;
    height: 50rpx;
  }

  .user-level {
    gap: 12rpx;
  }

  .level-text {
    font-size: 22rpx;
    padding: 4rpx 10rpx;
  }

  .points-text {
    font-size: 22rpx;
  }

  .assets-overview {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    padding: 24rpx;
  }

  .function-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .order-status-list {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xs);
  }

  .order-status-item {
    padding: var(--spacing-xs);
  }

  .status-icon {
    font-size: 40rpx;
  }

  .status-text {
    font-size: 20rpx;
  }
}