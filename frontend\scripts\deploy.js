// scripts/deploy.js
// 快速部署配置脚本

const fs = require('fs');
const path = require('path');

// 配置文件路径
const CONFIG_FILE = path.join(__dirname, '../config/env.js');

// 预设的服务器配置
const SERVER_PRESETS = {
  local: {
    name: '本地开发',
    baseUrl: 'http://localhost:5000',
    apiUrl: 'http://localhost:5000/api',
    imageUrl: 'http://localhost:5000/images',
    videoUrl: 'http://localhost:5000/videos',
    uploadUrl: 'http://localhost:5000/api/upload',
    wsUrl: 'ws://localhost:5000/ws',
    debug: true
  },
  current: {
    name: '当前服务器',
    baseUrl: 'http://************:5000',
    apiUrl: 'http://************:5000/api',
    imageUrl: 'http://************:5000/images',
    videoUrl: 'http://************:5000/videos',
    uploadUrl: 'http://************:5000/api/upload',
    wsUrl: 'ws://************:5000/ws',
    debug: false
  }
};

/**
 * 读取当前配置文件
 */
function readConfig() {
  try {
    const content = fs.readFileSync(CONFIG_FILE, 'utf8');
    return content;
  } catch (error) {
    console.error('读取配置文件失败:', error.message);
    return null;
  }
}

/**
 * 写入配置文件
 */
function writeConfig(content) {
  try {
    fs.writeFileSync(CONFIG_FILE, content, 'utf8');
    return true;
  } catch (error) {
    console.error('写入配置文件失败:', error.message);
    return false;
  }
}

/**
 * 更新服务器配置
 */
function updateServerConfig(serverUrl, environment = 'production') {
  const config = readConfig();
  if (!config) return false;

  // 解析URL
  let baseUrl, apiUrl, imageUrl, videoUrl, uploadUrl, wsUrl;
  
  if (serverUrl.includes('://')) {
    const protocol = serverUrl.split('://')[0];
    const wsProtocol = protocol === 'https' ? 'wss' : 'ws';
    
    baseUrl = serverUrl;
    apiUrl = `${serverUrl}/api`;
    imageUrl = `${serverUrl}/images`;
    videoUrl = `${serverUrl}/videos`;
    uploadUrl = `${serverUrl}/api/upload`;
    wsUrl = `${wsProtocol}://${serverUrl.split('://')[1]}/ws`;
  } else {
    // 默认使用http协议
    baseUrl = `http://${serverUrl}`;
    apiUrl = `http://${serverUrl}/api`;
    imageUrl = `http://${serverUrl}/images`;
    videoUrl = `http://${serverUrl}/videos`;
    uploadUrl = `http://${serverUrl}/api/upload`;
    wsUrl = `ws://${serverUrl}/ws`;
  }

  // 替换配置
  let newConfig = config;
  
  // 更新当前环境
  newConfig = newConfig.replace(
    /current:\s*['"][^'"]*['"]/,
    `current: '${environment}'`
  );

  // 更新生产环境配置
  const productionConfigRegex = /production:\s*{[^}]*}/s;
  const newProductionConfig = `production: {
    name: '生产环境',
    baseUrl: '${baseUrl}',
    apiUrl: '${apiUrl}',
    imageUrl: '${imageUrl}',
    videoUrl: '${videoUrl}',
    uploadUrl: '${uploadUrl}',
    wsUrl: '${wsUrl}',
    debug: false
  }`;
  
  newConfig = newConfig.replace(productionConfigRegex, newProductionConfig);

  return writeConfig(newConfig);
}

/**
 * 切换到预设配置
 */
function switchToPreset(presetName, environment = 'production') {
  const preset = SERVER_PRESETS[presetName];
  if (!preset) {
    console.error(`预设配置 "${presetName}" 不存在`);
    return false;
  }

  const config = readConfig();
  if (!config) return false;

  let newConfig = config;
  
  // 更新当前环境
  newConfig = newConfig.replace(
    /current:\s*['"][^'"]*['"]/,
    `current: '${environment}'`
  );

  // 更新对应环境的配置
  const envConfigRegex = new RegExp(`${environment}:\\s*{[^}]*}`, 's');
  const newEnvConfig = `${environment}: {
    name: '${preset.name}',
    baseUrl: '${preset.baseUrl}',
    apiUrl: '${preset.apiUrl}',
    imageUrl: '${preset.imageUrl}',
    videoUrl: '${preset.videoUrl}',
    uploadUrl: '${preset.uploadUrl}',
    wsUrl: '${preset.wsUrl}',
    debug: ${preset.debug}
  }`;
  
  newConfig = newConfig.replace(envConfigRegex, newEnvConfig);

  return writeConfig(newConfig);
}

/**
 * 显示当前配置
 */
function showCurrentConfig() {
  const config = readConfig();
  if (!config) return;

  // 提取当前环境
  const currentMatch = config.match(/current:\s*['"]([^'"]*)['"]/);
  const current = currentMatch ? currentMatch[1] : 'unknown';

  console.log('\n=== 当前配置 ===');
  console.log(`当前环境: ${current}`);

  // 提取对应环境的配置
  const envRegex = new RegExp(`${current}:\\s*{([^}]*)}`, 's');
  const envMatch = config.match(envRegex);
  
  if (envMatch) {
    const envConfig = envMatch[1];
    const baseUrlMatch = envConfig.match(/baseUrl:\s*['"]([^'"]*)['"]/);
    const apiUrlMatch = envConfig.match(/apiUrl:\s*['"]([^'"]*)['"]/);
    
    if (baseUrlMatch) console.log(`服务器地址: ${baseUrlMatch[1]}`);
    if (apiUrlMatch) console.log(`API地址: ${apiUrlMatch[1]}`);
  }
  console.log('==================\n');
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
使用方法:
  node deploy.js show                    # 显示当前配置
  node deploy.js local                   # 切换到本地开发环境
  node deploy.js current                 # 切换到当前服务器
  node deploy.js server <URL>            # 设置自定义服务器地址
  
示例:
  node deploy.js server 192.168.1.100:5000
  node deploy.js server https://api.yourdomain.com
    `);
    return;
  }

  const command = args[0];

  switch (command) {
    case 'show':
      showCurrentConfig();
      break;
      
    case 'local':
      if (switchToPreset('local', 'development')) {
        console.log('✅ 已切换到本地开发环境');
        showCurrentConfig();
      }
      break;
      
    case 'current':
      if (switchToPreset('current', 'production')) {
        console.log('✅ 已切换到当前服务器环境');
        showCurrentConfig();
      }
      break;
      
    case 'server':
      if (args.length < 2) {
        console.error('❌ 请提供服务器地址');
        return;
      }
      
      const serverUrl = args[1];
      if (updateServerConfig(serverUrl)) {
        console.log(`✅ 已更新服务器配置: ${serverUrl}`);
        showCurrentConfig();
      }
      break;
      
    default:
      console.error(`❌ 未知命令: ${command}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  updateServerConfig,
  switchToPreset,
  showCurrentConfig
};
