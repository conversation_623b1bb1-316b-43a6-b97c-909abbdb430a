{% extends "base.html" %}

{% block title %}图片管理 - 漆器文化商城管理后台{% endblock %}

{% block page_title %}图片管理{% endblock %}

{% block content %}
<!-- 上传区域 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-upload me-2"></i>
            上传图片
        </h5>
    </div>
    <div class="card-body">
        <div class="upload-area" id="uploadArea">
            <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
            <h5>拖拽图片到这里或点击上传</h5>
            <p class="text-muted">支持 PNG, JPG, JPEG, GIF, WEBP 格式，最大 16MB</p>
            <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
            <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-plus me-1"></i>选择图片
            </button>
        </div>
        
        <!-- 上传进度 -->
        <div id="uploadProgress" class="mt-3" style="display: none;">
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted mt-1 d-block">正在上传...</small>
        </div>
    </div>
</div>

<!-- 图片列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-images me-2"></i>
            图片库 ({{ images|length }} 张图片)
        </h5>
        <div class="btn-group">
            <button class="btn btn-sm btn-outline-secondary" onclick="toggleView('grid')" id="gridViewBtn">
                <i class="fas fa-th"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary active" onclick="toggleView('list')" id="listViewBtn">
                <i class="fas fa-list"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if images %}
            <!-- 网格视图 -->
            <div id="gridView" class="row" style="display: none;">
                {% for image in images %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card image-card">
                        <div class="image-container">
                            <img src="{{ image.path }}" class="card-img-top" alt="{{ image.filename }}"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMDAgNTBDMTEzLjI1NCA1MCAxMjQgNjAuNzQ2IDEyNCA3NEMxMjQgODcuMjU0IDExMy4yNTQgOTggMTAwIDk4Qzg2Ljc0NiA5OCA3NiA4Ny4yNTQgNzYgNzRDNzYgNjAuNzQ2IDg2Ljc0NiA1MCAxMDAgNTBaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik00MCA1MEgxNjBMMTQwIDEyMEg2MEw0MCA1MFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'">
                            <div class="image-overlay">
                                <button class="btn btn-sm btn-primary" onclick="copyImagePath('{{ image.path }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteImage('{{ image.filename }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-2">
                            <h6 class="card-title mb-1">{{ image.filename }}</h6>
                            <small class="text-muted">{{ "%.1f"|format(image.size / 1024) }} KB</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- 列表视图 -->
            <div id="listView" class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="80">预览</th>
                            <th>文件名</th>
                            <th>路径</th>
                            <th width="100">大小</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for image in images %}
                        <tr>
                            <td>
                                <img src="{{ image.path }}" class="image-preview" alt="{{ image.filename }}"
                                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMCAxNUMzMy45NzgyIDE1IDM3IDIwLjAyOTQgMzcgMjZDMzcgMzEuOTcwNiAzMy45NzgyIDM3IDMwIDM3QzI2LjAyMTggMzcgMjMgMzEuOTcwNiAyMyAyNkMyMyAyMC4wMjk0IDI2LjAyMTggMTUgMzAgMTVaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik0xMiA0NUg0OEw0MiAzNUgxOEwxMiA0NVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'">
                            </td>
                            <td>
                                <strong>{{ image.filename }}</strong>
                            </td>
                            <td>
                                <code class="text-muted">{{ image.path }}</code>
                                <button class="btn btn-sm btn-link p-0 ms-2" onclick="copyImagePath('{{ image.path }}')" title="复制路径">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ "%.1f"|format(image.size / 1024) }} KB</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="previewImage('{{ image.path }}', '{{ image.filename }}')" title="预览">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="copyImagePath('{{ image.path }}')" title="复制路径">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteImage('{{ image.filename }}')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">还没有上传任何图片</h5>
                <p class="text-muted">点击上方的上传按钮开始添加图片</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 图片预览模态框 -->
<div class="modal fade" id="imagePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalTitle">图片预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="previewImage" src="" class="img-fluid" alt="预览图片">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="copyImagePath(document.getElementById('previewImage').src)">
                    <i class="fas fa-copy me-1"></i>复制路径
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 文件上传处理
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');
    const uploadProgress = document.getElementById('uploadProgress');

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        handleFiles(files);
    });

    // 文件选择
    fileInput.addEventListener('change', function(e) {
        handleFiles(e.target.files);
    });

    // 处理文件上传
    function handleFiles(files) {
        if (files.length === 0) return;

        // 验证文件
        const validFiles = [];
        const invalidFiles = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileType = file.type.toLowerCase();
            const fileSize = file.size;

            // 检查文件类型
            if (!fileType.startsWith('image/')) {
                invalidFiles.push(`${file.name}: 不是图片文件`);
                continue;
            }

            // 检查文件大小 (16MB)
            if (fileSize > 16 * 1024 * 1024) {
                invalidFiles.push(`${file.name}: 文件过大 (超过16MB)`);
                continue;
            }

            validFiles.push(file);
        }

        // 显示无效文件警告
        if (invalidFiles.length > 0) {
            showError('以下文件无法上传：\n' + invalidFiles.join('\n'));
        }

        if (validFiles.length === 0) {
            return;
        }

        const formData = new FormData();
        for (let i = 0; i < validFiles.length; i++) {
            formData.append('file', validFiles[i]);
        }

        uploadProgress.style.display = 'block';
        const progressBar = uploadProgress.querySelector('.progress-bar');
        const progressText = uploadProgress.querySelector('small');
        progressText.textContent = `正在上传 ${validFiles.length} 个文件...`;

        fetch('/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(result => {
            uploadProgress.style.display = 'none';
            if (result.success) {
                let message = result.message;
                if (result.failed_files && result.failed_files.length > 0) {
                    message += '\n失败的文件：\n' + result.failed_files.map(f => `${f.filename}: ${f.error}`).join('\n');
                }
                showSuccess(message);
                setTimeout(() => location.reload(), 1500);
            } else {
                let errorMessage = result.message;
                if (result.failed_files && result.failed_files.length > 0) {
                    errorMessage += '\n详细错误：\n' + result.failed_files.map(f => `${f.filename}: ${f.error}`).join('\n');
                }
                showError(errorMessage);
            }
        })
        .catch(error => {
            uploadProgress.style.display = 'none';
            console.error('Upload error:', error);
            showError('上传失败：' + error.message);
        });

        // 重置文件输入
        fileInput.value = '';
    }

    // 切换视图
    function toggleView(viewType) {
        const gridView = document.getElementById('gridView');
        const listView = document.getElementById('listView');
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');

        if (viewType === 'grid') {
            gridView.style.display = 'block';
            listView.style.display = 'none';
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        } else {
            gridView.style.display = 'none';
            listView.style.display = 'block';
            gridBtn.classList.remove('active');
            listBtn.classList.add('active');
        }
    }

    // 复制图片路径
    function copyImagePath(path) {
        navigator.clipboard.writeText(path).then(function() {
            showSuccess('图片路径已复制到剪贴板！');
        }).catch(function(err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = path;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showSuccess('图片路径已复制到剪贴板！');
        });
    }

    // 删除图片
    function deleteImage(filename) {
        if (confirm('确定要删除这张图片吗？删除后无法恢复！')) {
            fetch('/api/delete_image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({filename: filename})
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccess('图片删除成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showError('删除失败：' + result.message);
                }
            })
            .catch(error => {
                showError('删除失败：' + error);
            });
        }
    }

    // 预览图片
    function previewImage(path, filename) {
        document.getElementById('previewImage').src = path;
        document.getElementById('previewModalTitle').textContent = filename;
        new bootstrap.Modal(document.getElementById('imagePreviewModal')).show();
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 默认显示列表视图
        toggleView('list');
    });
</script>

<style>
    .image-card {
        transition: transform 0.3s ease;
        overflow: hidden;
    }

    .image-card:hover {
        transform: translateY(-5px);
    }

    .image-container {
        position: relative;
        overflow: hidden;
        height: 200px;
    }

    .image-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .image-container:hover .image-overlay {
        opacity: 1;
    }

    .image-container:hover img {
        transform: scale(1.1);
    }
</style>
{% endblock %}
