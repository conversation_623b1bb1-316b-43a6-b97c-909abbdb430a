<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片管理 - 小程序管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .image-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .image-card:hover {
            transform: translateY(-5px);
        }
        .image-preview {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-info {
            font-size: 0.8rem;
            color: #666;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">小程序管理后台</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link" href="/content">内容管理</a>
                <a class="nav-link active" href="/image-manager">图片管理</a>
                <a class="nav-link" href="/videos">视频管理</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>图片管理</h2>
            <div class="btn-group">
                <button class="btn btn-success" onclick="refreshImages()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button class="btn btn-info" onclick="copyAllPaths()">
                    <i class="fas fa-copy"></i> 复制所有路径
                </button>
            </div>
        </div>

        <!-- 上传区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">上传图片</h5>
            </div>
            <div class="card-body">
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>拖拽图片到这里或点击上传</h5>
                    <p class="text-muted">支持 PNG, JPG, JPEG, GIF, WEBP 格式</p>
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                        <i class="fas fa-plus"></i> 选择图片
                    </button>
                </div>
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">图片列表 (<span id="imageCount">0</span>)</h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary active" onclick="setViewMode('grid')" id="gridBtn">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="setViewMode('list')" id="listBtn">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="imageGrid" class="row">
                    <!-- 图片将在这里动态加载 -->
                </div>
                <div id="imageList" class="d-none">
                    <!-- 列表视图将在这里显示 -->
                </div>
                <div id="loadingSpinner" class="text-center py-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                <div id="emptyState" class="text-center py-4 d-none">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无图片</h5>
                    <p class="text-muted">上传一些图片开始使用吧！</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片详情模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <img id="modalImage" class="img-fluid rounded" alt="图片预览">
                        </div>
                        <div class="col-md-4">
                            <h6>文件信息</h6>
                            <table class="table table-sm">
                                <tr><td>文件名:</td><td id="modalFilename"></td></tr>
                                <tr><td>大小:</td><td id="modalFileSize"></td></tr>
                                <tr><td>URL:</td><td><small id="modalUrl"></small></td></tr>
                            </table>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="copyImagePath()">
                                    <i class="fas fa-copy"></i> 复制路径
                                </button>
                                <button class="btn btn-outline-primary" onclick="openImageInNewTab()">
                                    <i class="fas fa-external-link-alt"></i> 新窗口打开
                                </button>
                                <button class="btn btn-danger" onclick="deleteCurrentImage()">
                                    <i class="fas fa-trash"></i> 删除图片
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentImages = [];
        let currentViewMode = 'grid';
        let currentImageData = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadImages();
            setupUploadHandlers();
        });

        // 加载图片列表
        function loadImages() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('emptyState').style.display = 'none';
            
            fetch('/api/list_images')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentImages = data.images;
                        displayImages();
                    } else {
                        showError('加载图片列表失败: ' + data.message);
                    }
                })
                .catch(error => {
                    showError('加载图片列表失败: ' + error);
                })
                .finally(() => {
                    document.getElementById('loadingSpinner').style.display = 'none';
                });
        }

        // 显示图片
        function displayImages() {
            const imageCount = document.getElementById('imageCount');
            imageCount.textContent = currentImages.length;

            if (currentImages.length === 0) {
                document.getElementById('emptyState').style.display = 'block';
                document.getElementById('imageGrid').innerHTML = '';
                return;
            }

            if (currentViewMode === 'grid') {
                displayGridView();
            } else {
                displayListView();
            }
        }

        // 网格视图
        function displayGridView() {
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';

            currentImages.forEach(image => {
                const col = document.createElement('div');
                col.className = 'col-md-3 col-sm-6 mb-4';
                col.innerHTML = `
                    <div class="card image-card" onclick="showImageModal('${image.filename}')">
                        <img src="${image.url}" class="image-preview" alt="${image.filename}" 
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMDAgNTBDMTEzLjI1NCA1MCAxMjQgNjAuNzQ2IDEyNCA3NEMxMjQgODcuMjU0IDExMy4yNTQgOTggMTAwIDk4Qzg2Ljc0NiA5OCA3NiA4Ny4yNTQgNzYgNzRDNzYgNjAuNzQ2IDg2Ljc0NiA1MCAxMDAgNTBaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik00MCAx NTBIMTYwTDE0MCAxMjBINjBMNDAgMTUwWiIgZmlsbD0iI0NDQ0NDQyIvPgo8L3N2Zz4='">
                        <div class="card-body p-2">
                            <h6 class="card-title mb-1" style="font-size: 0.9rem;">${image.filename}</h6>
                            <div class="file-info">
                                <small>${formatFileSize(image.size)}</small>
                            </div>
                        </div>
                    </div>
                `;
                grid.appendChild(col);
            });
        }

        // 列表视图
        function displayListView() {
            const list = document.getElementById('imageList');
            list.innerHTML = '';

            const table = document.createElement('table');
            table.className = 'table table-hover';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>预览</th>
                        <th>文件名</th>
                        <th>大小</th>
                        <th>URL</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody></tbody>
            `;

            const tbody = table.querySelector('tbody');
            currentImages.forEach(image => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <img src="${image.url}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;" 
                             alt="${image.filename}" onclick="showImageModal('${image.filename}')">
                    </td>
                    <td>${image.filename}</td>
                    <td>${formatFileSize(image.size)}</td>
                    <td><small>${image.url}</small></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="copyPath('${image.url}')">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteImage('${image.filename}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            list.appendChild(table);
        }

        // 设置视图模式
        function setViewMode(mode) {
            currentViewMode = mode;
            
            document.getElementById('gridBtn').classList.toggle('active', mode === 'grid');
            document.getElementById('listBtn').classList.toggle('active', mode === 'list');
            
            document.getElementById('imageGrid').classList.toggle('d-none', mode === 'list');
            document.getElementById('imageList').classList.toggle('d-none', mode === 'grid');
            
            displayImages();
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 显示成功消息
        function showSuccess(message) {
            // 这里可以使用 toast 或其他通知组件
            alert('成功: ' + message);
        }

        // 显示错误消息
        function showError(message) {
            alert('错误: ' + message);
        }

        // 设置上传处理器
        function setupUploadHandlers() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 拖拽上传
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                uploadFiles(files);
            });

            // 文件选择上传
            fileInput.addEventListener('change', function(e) {
                uploadFiles(e.target.files);
            });
        }

        // 上传文件
        function uploadFiles(files) {
            if (files.length === 0) return;

            const formData = new FormData();
            for (let file of files) {
                formData.append('file', file);
            }

            const progressBar = document.querySelector('.progress-bar');
            const uploadProgress = document.getElementById('uploadProgress');

            uploadProgress.style.display = 'block';
            progressBar.style.width = '0%';

            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadImages(); // 重新加载图片列表
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                showError('上传失败: ' + error);
            })
            .finally(() => {
                uploadProgress.style.display = 'none';
                document.getElementById('fileInput').value = '';
            });
        }

        // 显示图片模态框
        function showImageModal(filename) {
            const image = currentImages.find(img => img.filename === filename);
            if (!image) return;

            currentImageData = image;

            document.getElementById('modalImage').src = image.url;
            document.getElementById('modalFilename').textContent = image.filename;
            document.getElementById('modalFileSize').textContent = formatFileSize(image.size);
            document.getElementById('modalUrl').textContent = image.url;

            new bootstrap.Modal(document.getElementById('imageModal')).show();
        }

        // 复制图片路径
        function copyImagePath() {
            if (!currentImageData) return;
            copyPath(currentImageData.url);
        }

        // 复制路径到剪贴板
        function copyPath(path) {
            navigator.clipboard.writeText(path).then(() => {
                showSuccess('路径已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = path;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showSuccess('路径已复制到剪贴板');
            });
        }

        // 在新窗口打开图片
        function openImageInNewTab() {
            if (!currentImageData) return;
            window.open(currentImageData.url, '_blank');
        }

        // 删除当前图片
        function deleteCurrentImage() {
            if (!currentImageData) return;
            deleteImage(currentImageData.filename);
        }

        // 删除图片
        function deleteImage(filename) {
            if (!confirm('确定要删除这张图片吗？此操作不可撤销。')) {
                return;
            }

            fetch('/api/delete_image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filename: filename })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadImages(); // 重新加载图片列表
                    // 如果模态框打开着，关闭它
                    const modal = bootstrap.Modal.getInstance(document.getElementById('imageModal'));
                    if (modal) {
                        modal.hide();
                    }
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                showError('删除失败: ' + error);
            });
        }

        // 复制所有图片路径
        function copyAllPaths() {
            if (currentImages.length === 0) {
                showError('没有图片可复制');
                return;
            }

            const paths = currentImages.map(img => img.url).join('\n');
            copyPath(paths);
        }

        // 刷新图片列表
        function refreshImages() {
            loadImages();
        }
    </script>
</body>
</html>
