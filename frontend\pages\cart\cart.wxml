<!-- pages/cart/cart.wxml -->
<view class="cart-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">购物车</text>
  </view>

  <!-- 购物车内容 -->
  <view class="cart-content">
    <!-- 购物车商品列表 -->
    <view class="cart-items" wx:if="{{cartItems.length > 0}}">
      <view
        class="cart-item card"
        wx:for="{{cartItems}}"
        wx:key="id"
      >
        <!-- 选择框 -->
        <view class="item-checkbox" bindtap="onToggleSelect" data-id="{{item.id}}">
          <view class="checkbox {{item.selected ? 'checked' : ''}}">
            <text class="checkbox-icon" wx:if="{{item.selected}}">✓</text>
          </view>
        </view>

        <!-- 商品图片 -->
        <view class="item-image">
          <image src="{{item.image}}" mode="aspectFill" lazy-load="{{true}}" />
        </view>

        <!-- 商品信息 -->
        <view class="item-info">
          <text class="item-name">{{item.name}}</text>
          <text class="item-price">¥{{item.price}}</text>

          <!-- 数量控制 -->
          <view class="item-controls">
            <view class="quantity-controls">
              <button
                class="quantity-btn"
                bindtap="onQuantityChange"
                data-id="{{item.id}}"
                data-type="decrease"
                disabled="{{item.quantity <= 1}}"
              >-</button>
              <input
                class="quantity-input"
                type="number"
                value="{{item.quantity}}"
                bindinput="onQuantityInput"
                data-id="{{item.id}}"
                min="1"
              />
              <button
                class="quantity-btn primary"
                bindtap="onQuantityChange"
                data-id="{{item.id}}"
                data-type="increase"
              >+</button>
            </view>

            <!-- 删除按钮 -->
            <button
              class="delete-btn"
              bindtap="onDeleteItem"
              data-id="{{item.id}}"
            >🗑</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 购物车空状态 -->
    <view class="cart-empty" wx:if="{{cartItems.length === 0}}">
      <view class="empty-icon">🛒</view>
      <text class="empty-text">购物车还是空的</text>
      <button class="go-shopping-btn" bindtap="onGoShopping">去逛逛</button>
    </view>
  </view>

  <!-- 购物车底部结算栏 -->
  <view class="cart-footer" wx:if="{{cartItems.length > 0}}">
    <!-- 全选 -->
    <view class="select-all" bindtap="onToggleSelectAll">
      <view class="checkbox {{allSelected ? 'checked' : ''}}">
        <text class="checkbox-icon" wx:if="{{allSelected}}">✓</text>
      </view>
      <text class="select-all-text">全选</text>
    </view>

    <!-- 合计 -->
    <view class="total-section">
      <view class="total-info">
        <text class="total-label">合计：</text>
        <text class="total-amount">¥{{totalAmount}}</text>
      </view>
      <text class="selected-count">已选{{selectedCount}}件</text>
    </view>

    <!-- 结算按钮 -->
    <button
      class="checkout-btn {{selectedCount > 0 ? '' : 'disabled'}}"
      bindtap="onCheckout"
      disabled="{{selectedCount === 0}}"
    >
      结算({{selectedCount}})
    </button>
  </view>
</view>

<!-- 删除确认弹窗 -->
<view class="modal-overlay" wx:if="{{showDeleteModal}}" bindtap="onCancelDelete">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">确认删除</text>
    </view>
    <view class="modal-body">
      <text class="modal-text">确定要删除这件商品吗？</text>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCancelDelete">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmDelete">删除</button>
    </view>
  </view>
</view>