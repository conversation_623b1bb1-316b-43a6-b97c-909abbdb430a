<!--pages/product-detail/product-detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 商品详情内容 -->
  <view wx:else class="product-detail">
    <!-- 商品图片轮播 -->
    <view class="product-images">
      <swiper 
        class="image-swiper" 
        indicator-dots="{{product.images.length > 1}}"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="#fff"
        autoplay="{{false}}"
        circular="{{true}}"
        bindchange="onSwiperChange"
      >
        <swiper-item wx:for="{{product.images}}" wx:key="index">
          <image 
            src="{{item}}" 
            mode="aspectFill" 
            class="product-image"
            bindtap="onImageTap"
            data-index="{{index}}"
            lazy-load="{{true}}"
          />
        </swiper-item>
      </swiper>
      
      <!-- 图片指示器 -->
      <view wx:if="{{product.images.length > 1}}" class="image-indicator">
        {{currentImageIndex + 1}} / {{product.images.length}}
      </view>
    </view>

    <!-- 商品基本信息 -->
    <view class="product-info card">
      <view class="product-price">
        <text class="current-price">¥{{product.price}}</text>
        <text wx:if="{{product.original_price}}" class="original-price">¥{{product.original_price}}</text>
      </view>
      
      <view class="product-title">{{product.name}}</view>
      
      <view class="product-subtitle">{{product.description}}</view>
      
      <view class="product-stats">
        <view class="stat-item">
          <text class="stat-label">销量</text>
          <text class="stat-value">{{product.sales}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">库存</text>
          <text class="stat-value">{{product.stock}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">评分</text>
          <text class="stat-value">{{product.rating}}</text>
          <text class="stat-unit">★</text>
        </view>
      </view>
    </view>

    <!-- 数量选择 -->
    <view class="quantity-selector card">
      <view class="selector-label">购买数量</view>
      <view class="quantity-controls">
        <button 
          class="quantity-btn minus {{quantity <= 1 ? 'disabled' : ''}}" 
          bindtap="onQuantityChange" 
          data-type="minus"
        >-</button>
        <input class="quantity-input" type="number" value="{{quantity}}" disabled />
        <button 
          class="quantity-btn plus {{quantity >= 99 ? 'disabled' : ''}}" 
          bindtap="onQuantityChange" 
          data-type="plus"
        >+</button>
      </view>
    </view>

    <!-- 详情标签页 -->
    <view class="detail-tabs card">
      <view class="tab-headers">
        <view 
          class="tab-header {{activeTab === 'detail' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="detail"
        >商品详情</view>
        <view 
          class="tab-header {{activeTab === 'specs' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="specs"
        >规格参数</view>
        <view 
          class="tab-header {{activeTab === 'reviews' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="reviews"
        >用户评价</view>
      </view>
      
      <view class="tab-content">
        <!-- 商品详情 -->
        <view wx:if="{{activeTab === 'detail'}}" class="detail-content">
          <view wx:if="{{product.detail.story}}" class="product-story">
            <view class="story-title">商品故事</view>
            <view class="story-content">{{product.detail.story}}</view>
          </view>
          
          <view wx:if="{{product.detail.features.length > 0}}" class="product-features">
            <view class="features-title">产品特色</view>
            <view class="features-list">
              <view wx:for="{{product.detail.features}}" wx:key="index" class="feature-item">
                <text class="feature-icon">✓</text>
                <text class="feature-text">{{item}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 规格参数 -->
        <view wx:if="{{activeTab === 'specs'}}" class="specs-content">
          <view class="specs-table">
            <view wx:if="{{product.detail.materials}}" class="spec-row">
              <view class="spec-label">材料</view>
              <view class="spec-value">{{product.detail.materials}}</view>
            </view>
            <view wx:if="{{product.detail.size}}" class="spec-row">
              <view class="spec-label">尺寸</view>
              <view class="spec-value">{{product.detail.size}}</view>
            </view>
            <view wx:if="{{product.detail.weight}}" class="spec-row">
              <view class="spec-label">重量</view>
              <view class="spec-value">{{product.detail.weight}}</view>
            </view>
            <view wx:if="{{product.detail.origin}}" class="spec-row">
              <view class="spec-label">产地</view>
              <view class="spec-value">{{product.detail.origin}}</view>
            </view>
            <view wx:if="{{product.detail.craft}}" class="spec-row">
              <view class="spec-label">工艺</view>
              <view class="spec-value">{{product.detail.craft}}</view>
            </view>
          </view>
        </view>
        
        <!-- 用户评价 -->
        <view wx:if="{{activeTab === 'reviews'}}" class="reviews-content">
          <view class="reviews-summary">
            <view class="rating-score">{{product.rating}}</view>
            <view class="rating-stars">★★★★★</view>
            <view class="rating-count">{{product.reviews}}条评价</view>
          </view>
          
          <view class="reviews-placeholder">
            <text class="placeholder-text">暂无评价，快来抢沙发吧~</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 相关推荐 -->
    <view wx:if="{{relatedProducts.length > 0}}" class="related-products card">
      <view class="section-title">相关推荐</view>
      <scroll-view class="related-scroll" scroll-x="{{true}}">
        <view class="related-list">
          <view 
            wx:for="{{relatedProducts}}" 
            wx:key="id" 
            class="related-item"
            bindtap="onRelatedProductTap"
            data-id="{{item.id}}"
          >
            <image src="{{item.image}}" mode="aspectFill" class="related-image" />
            <view class="related-name">{{item.name}}</view>
            <view class="related-price">¥{{item.price}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-left">
      <button class="action-btn service" bindtap="onContactService">
        <text class="action-icon">💬</text>
        <text class="action-text">客服</text>
      </button>
      <button class="action-btn favorite {{product.isFavorite ? 'active' : ''}}" bindtap="onToggleFavorite">
        <text class="action-icon">{{product.isFavorite ? '❤️' : '🤍'}}</text>
        <text class="action-text">收藏</text>
      </button>
      <button class="action-btn cart" bindtap="onAddToCart">
        <text class="action-icon">🛒</text>
        <text class="action-text">购物车</text>
        <view wx:if="{{cartCount > 0}}" class="cart-badge">{{cartCount}}</view>
      </button>
    </view>
    
    <view class="action-right">
      <button class="buy-btn add-cart" bindtap="onAddToCart">加入购物车</button>
      <button class="buy-btn buy-now" bindtap="onBuyNow">立即购买</button>
    </view>
  </view>
</view>
