#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漆器文化商城小程序 - 后台管理系统
Flask应用主文件
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
import os
import json
import shutil
from datetime import datetime
import uuid

try:
    from default_data import get_default_data
except ImportError:
    def get_default_data():
        return {}

app = Flask(__name__)
app.secret_key = 'lacquerware_admin_secret_key_2024'

# 启用CORS支持，允许小程序跨域访问
CORS(app, origins=['*'], methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
ALLOWED_VIDEO_EXTENSIONS = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
MAX_VIDEO_SIZE = 50 * 1024 * 1024  # 50MB

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_VIDEO_SIZE  # 使用视频文件的大小限制 (100MB)

# 后端数据文件路径
BACKEND_DATA_PATH = 'data/'
BACKEND_UPLOADS_PATH = 'uploads/'
FRONTEND_DATA_FILE = '../frontend/data/miniprogram_data.json'
DATA_FILE = 'data/miniprogram_data.json'

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def allowed_video_file(filename):
    """检查视频文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_VIDEO_EXTENSIONS

def load_miniprogram_data():
    """加载小程序数据"""
    try:
        # 优先从前端数据文件加载
        if os.path.exists(FRONTEND_DATA_FILE):
            with open(FRONTEND_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        # 如果前端文件不存在，尝试后端文件
        elif os.path.exists(DATA_FILE):
            with open(DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载数据失败: {e}")
    
    # 如果有默认数据文件，使用它
    try:
        default_data = get_default_data()
        if default_data:
            return default_data
    except:
        pass

    # 返回基础默认数据
    return {
        "app_config": {
            "app_name": "非物质文化遗产·漆器",
            "primary_color": "#FF8C42",
            "secondary_color": "#FFF8F0"
        },
        "home_data": {
            "guide_data": {
                "title": "漆器收藏与鉴赏指南",
                "expert_name": "张文华 专家",
                "publish_date": "2024年1月15日",
                "cover_image": "/images/guide-cover.jpg"
            },
            "quick_actions": [
                {"id": 1, "icon": "🎨", "text": "工艺介绍", "action": "craft"},
                {"id": 2, "icon": "📦", "text": "商品分类", "action": "category"},
                {"id": 3, "icon": "✨", "text": "定制服务", "action": "custom"}
            ],
            "video_list": [
                {
                    "id": 1,
                    "title": "传统漆器制作工艺",
                    "thumbnail": "http://127.0.0.1:5000/images/video1.jpg",
                    "duration": "05:32",
                    "url": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                    "showPlayer": False
                },
                {
                    "id": 2,
                    "title": "漆器文化的传承与发展",
                    "thumbnail": "http://127.0.0.1:5000/images/video2.jpg",
                    "duration": "08:15",
                    "url": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                    "showPlayer": False
                },
                {
                    "id": 3,
                    "title": "现代漆器艺术创新",
                    "thumbnail": "http://127.0.0.1:5000/images/video3.jpg",
                    "duration": "06:48",
                    "url": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
                    "showPlayer": False
                }
            ],
            "timeline_data": [
                {"id": 1, "year": "2022年", "description": "平台正式上线，开始传承漆器文化"},
                {"id": 2, "year": "2023年", "description": "新增定制服务，满足个性化需求"},
                {"id": 3, "year": "2024年", "description": "入驻非遗名录，获得官方认证"}
            ],
            "recommend_products": [
                {
                    "id": 1,
                    "name": "传统漆扇 · 牡丹花开",
                    "price": "288.00",
                    "image": "/images/product1.jpg",
                    "category": "fan"
                },
                {
                    "id": 2,
                    "name": "精美手镯盒 · 凤凰于飞",
                    "price": "168.00",
                    "image": "/images/product2.jpg",
                    "category": "box"
                }
            ]
        },
        "category_data": {
            "categories": [
                {"id": 1, "name": "漆扇", "count": 12, "category": "fan"},
                {"id": 2, "name": "手镯盒", "count": 8, "category": "box"},
                {"id": 3, "name": "笔筒", "count": 15, "category": "pen"},
                {"id": 4, "name": "茶具", "count": 20, "category": "tea"},
                {"id": 5, "name": "首饰盒", "count": 10, "category": "jewelry"},
                {"id": 6, "name": "装饰品", "count": 25, "category": "decoration"}
            ]
        },
        "user_data": {
            "default_user": {
                "nick_name": "张三",
                "avatar_url": "/images/avatar.jpg",
                "phone": "138****8888",
                "level": "VIP",
                "points": 1280
            },
            "assets": {
                "coupons": 3,
                "points": 1280,
                "balance": "128.50",
                "cards": 2
            }
        }
    }

def save_miniprogram_data(data):
    """保存小程序数据到前端和后端"""
    try:
        # 保存到前端数据文件
        os.makedirs(os.path.dirname(FRONTEND_DATA_FILE), exist_ok=True)
        with open(FRONTEND_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        # 同时保存到后端数据文件作为备份
        os.makedirs(os.path.dirname(DATA_FILE), exist_ok=True)
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

def sync_to_miniprogram():
    """同步数据到小程序"""
    try:
        data = load_miniprogram_data()

        # 现在小程序通过API获取数据，只需要更新样式配置
        # 更新全局配置（主要是主题色）
        config_success = update_app_config(data)

        if config_success:
            print("数据同步成功 - 小程序将通过API获取最新数据")
            return True
        else:
            print("配置同步失败")
            return False

    except Exception as e:
        print(f"同步到小程序失败: {e}")
        return False

def update_home_page_data(data):
    """更新首页数据"""
    home_js_path = os.path.join(os.path.dirname(__file__), "../frontend/pages/home/<USER>")
    if not os.path.exists(home_js_path):
        return False

    try:
        with open(home_js_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 更新指南数据
        guide_data = data['home_data']['guide_data']
        guide_js = f"""    guideData: {{
      title: '{guide_data['title']}',
      expertName: '{guide_data['expert_name']}',
      publishDate: '{guide_data['publish_date']}',
      coverImage: '{guide_data['cover_image']}'
    }},"""

        # 更新快捷入口数据
        quick_actions = data['home_data']['quick_actions']
        actions_js = "    quickActions: [\n"
        for action in quick_actions:
            actions_js += f"""      {{
        id: {action['id']},
        icon: '{action['icon']}',
        text: '{action['text']}',
        action: '{action['action']}'
      }},\n"""
        actions_js += "    ],"

        # 更新视频列表数据
        video_list = data['home_data']['video_list']
        videos_js = "    videoList: [\n"
        for video in video_list:
            videos_js += f"""      {{
        id: {video['id']},
        title: '{video['title']}',
        thumbnail: '{video['thumbnail']}',
        duration: '{video['duration']}',
        url: 'https://example.com/video{video['id']}.mp4'
      }},\n"""
        videos_js += "    ],"

        # 更新时间线数据
        timeline_data = data['home_data']['timeline_data']
        timeline_js = "    timelineData: [\n"
        for item in timeline_data:
            timeline_js += f"""      {{
        id: {item['id']},
        year: '{item['year']}',
        description: '{item['description']}'
      }},\n"""
        timeline_js += "    ],"

        # 更新推荐商品数据
        recommend_products = data['home_data']['recommend_products']
        products_js = "    recommendProducts: [\n"
        for product in recommend_products:
            products_js += f"""      {{
        id: {product['id']},
        name: '{product['name']}',
        price: '{product['price']}',
        image: '{product['image']}',
        category: '{product['category']}'
      }},\n"""
        products_js += "    ]"

        # 使用正则表达式替换数据
        import re

        # 替换指南数据
        content = re.sub(
            r'guideData:\s*{[^}]*},',
            guide_js,
            content,
            flags=re.DOTALL
        )

        # 替换快捷入口数据
        content = re.sub(
            r'quickActions:\s*\[[^\]]*\],',
            actions_js,
            content,
            flags=re.DOTALL
        )

        # 替换视频列表数据
        content = re.sub(
            r'videoList:\s*\[[^\]]*\],',
            videos_js,
            content,
            flags=re.DOTALL
        )

        # 替换时间线数据
        content = re.sub(
            r'timelineData:\s*\[[^\]]*\],',
            timeline_js,
            content,
            flags=re.DOTALL
        )

        # 替换推荐商品数据
        content = re.sub(
            r'recommendProducts:\s*\[[^\]]*\]',
            products_js,
            content,
            flags=re.DOTALL
        )

        # 写回文件
        with open(home_js_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return True
    except Exception as e:
        print(f"更新首页数据失败: {e}")
        return False

def update_category_page_data(data):
    """更新分类页面数据"""
    category_js_path = os.path.join(os.path.dirname(__file__), "../frontend/pages/category/category.js")
    if not os.path.exists(category_js_path):
        return False

    try:
        with open(category_js_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 更新分类数据
        categories = data['category_data']['categories']
        categories_js = "    categories: [\n"
        for category in categories:
            categories_js += f"""      {{
        id: {category['id']},
        name: '{category['name']}',
        count: {category['count']},
        category: '{category['category']}',
        icon: '📦'
      }},\n"""
        categories_js += "    ]"

        # 使用正则表达式替换数据
        import re
        content = re.sub(
            r'categories:\s*\[[^\]]*\]',
            categories_js,
            content,
            flags=re.DOTALL
        )

        # 写回文件
        with open(category_js_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return True
    except Exception as e:
        print(f"更新分类页面数据失败: {e}")
        return False

def update_profile_page_data(data):
    """更新用户中心数据"""
    profile_js_path = os.path.join(os.path.dirname(__file__), "../frontend/pages/profile/profile.js")
    if not os.path.exists(profile_js_path):
        return False

    try:
        with open(profile_js_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 更新用户信息
        user_info = data['user_data']['default_user']
        assets = data['user_data']['assets']

        user_info_js = f"""    userInfo: {{
      nickName: '{user_info['nick_name']}',
      avatarUrl: '{user_info['avatar_url']}',
      phone: '{user_info['phone']}',
      level: '{user_info['level']}',
      points: {user_info['points']}
    }},"""

        assets_js = f"""    assets: {{
      coupons: {assets['coupons']},
      points: {assets['points']},
      balance: '{assets['balance']}',
      cards: {assets['cards']}
    }},"""

        # 使用正则表达式替换数据
        import re
        content = re.sub(
            r'userInfo:\s*{[^}]*},',
            user_info_js,
            content,
            flags=re.DOTALL
        )

        content = re.sub(
            r'assets:\s*{[^}]*},',
            assets_js,
            content,
            flags=re.DOTALL
        )

        # 写回文件
        with open(profile_js_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return True
    except Exception as e:
        print(f"更新用户中心数据失败: {e}")
        return False

def update_video_config():
    """自动扫描videos目录并更新视频配置"""
    try:
        # 加载当前配置
        data = load_miniprogram_data()

        # 扫描videos目录
        videos_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'videos')
        videos_dir = os.path.normpath(videos_dir)

        if not os.path.exists(videos_dir):
            return

        # 获取所有视频文件
        video_files = []
        for filename in os.listdir(videos_dir):
            if filename.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv')):
                # 跳过占位符文件
                if not filename.endswith('.placeholder'):
                    video_files.append(filename)

        # 更新视频列表配置
        video_list = []
        for i, filename in enumerate(video_files[:3]):  # 最多3个视频
            # 尝试从文件名推断标题
            name_without_ext = os.path.splitext(filename)[0]
            title = name_without_ext.replace('_', ' ').replace('-', ' ').title()

            # 如果标题太长，截取前20个字符
            if len(title) > 20:
                title = title[:20] + '...'

            # 尝试找到对应的缩略图，如果没有则使用默认图片
            thumbnail_candidates = [
                f"video{i + 1}.jpg",
                f"video{i + 1}.png",
                f"{name_without_ext}.jpg",
                f"{name_without_ext}.png"
            ]

            thumbnail_url = f"http://127.0.0.1:5000/images/video{i + 1}.jpg"  # 默认缩略图

            # 检查是否存在对应的缩略图文件
            images_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'images')
            for candidate in thumbnail_candidates:
                candidate_path = os.path.join(images_dir, candidate)
                if os.path.exists(candidate_path):
                    thumbnail_url = f"http://127.0.0.1:5000/images/{candidate}"
                    break

            video_list.append({
                'id': i + 1,
                'title': title,
                'thumbnail': thumbnail_url,  # 使用网络地址作为poster
                'duration': '00:00',  # 默认时长，可以后续手动修改
                'url': f"http://127.0.0.1:5000/videos/{filename}",  # 使用网络地址
                'showPlayer': False
            })

        # 如果没有找到视频文件，保持原有配置
        if video_list:
            data['home_data']['video_list'] = video_list
            save_miniprogram_data(data)
            print(f"已更新视频配置，找到 {len(video_list)} 个视频文件")

    except Exception as e:
        print(f"更新视频配置失败: {e}")

def update_app_config(data):
    """更新应用配置"""
    # 使用绝对路径
    app_wxss_path = os.path.join(os.path.dirname(__file__), "../frontend/app.wxss")
    app_wxss_path = os.path.normpath(app_wxss_path)

    print(f"尝试更新样式文件: {app_wxss_path}")

    if not os.path.exists(app_wxss_path):
        print(f"app.wxss文件不存在: {app_wxss_path}")
        return False

    try:
        with open(app_wxss_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 更新颜色变量
        app_config = data['app_config']
        primary_color = app_config.get('primary_color', '#FF8C42')
        secondary_color = app_config.get('secondary_color', '#FFF8F0')

        print(f"更新主题色: {primary_color}, 辅助色: {secondary_color}")

        # 使用更灵活的正则表达式替换颜色变量
        import re

        # 替换主题色 - 匹配任何颜色格式
        content = re.sub(
            r'(--primary-orange:\s*)[^;]+;',
            f'\\1{primary_color};',
            content
        )

        # 替换辅助色 - 匹配任何颜色格式
        content = re.sub(
            r'(--cream-white:\s*)[^;]+;',
            f'\\1{secondary_color};',
            content
        )

        # 写回文件
        with open(app_wxss_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print("应用配置更新成功")
        return True
    except Exception as e:
        print(f"更新应用配置失败: {e}")
        return False

# 静态文件服务
@app.route('/videos/<path:filename>')
def serve_video(filename):
    """提供视频文件服务"""
    try:
        video_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'videos')
        video_dir = os.path.normpath(video_dir)
        print(f"尝试访问视频文件: {filename}, 目录: {video_dir}")

        if not os.path.exists(video_dir):
            print(f"视频目录不存在: {video_dir}")
            return "Video directory not found", 404

        file_path = os.path.join(video_dir, filename)
        if not os.path.exists(file_path):
            print(f"视频文件不存在: {file_path}")
            return "Video file not found", 404

        return send_from_directory(video_dir, filename)
    except Exception as e:
        print(f"视频文件服务错误: {e}")
        return f"Error serving video: {str(e)}", 500

@app.route('/images/<path:filename>')
def serve_image(filename):
    """提供图片文件服务"""
    try:
        # 首先尝试从前端images目录获取
        frontend_image_dir = os.path.join(os.path.dirname(__file__), '../frontend/images')
        frontend_image_dir = os.path.normpath(frontend_image_dir)
        frontend_file_path = os.path.join(frontend_image_dir, filename)

        if os.path.exists(frontend_file_path):
            print(f"从前端目录提供图片: {filename}")
            return send_from_directory(frontend_image_dir, filename)

        # 如果前端目录没有，尝试从后端uploads目录获取
        backend_image_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'images')
        backend_image_dir = os.path.normpath(backend_image_dir)
        backend_file_path = os.path.join(backend_image_dir, filename)

        if os.path.exists(backend_file_path):
            print(f"从后端上传目录提供图片: {filename}")
            return send_from_directory(backend_image_dir, filename)

        print(f"图片文件不存在: {filename}")
        print(f"已检查目录: {frontend_image_dir}, {backend_image_dir}")
        return "Image file not found", 404

    except Exception as e:
        print(f"图片文件服务错误: {e}")
        return f"Error serving image: {str(e)}", 500

# 路由定义
@app.route('/')
def index():
    """管理后台首页"""
    data = load_miniprogram_data()
    return render_template('index.html', data=data)

@app.route('/test-paths')
def test_paths():
    """测试路径配置"""
    try:
        current_dir = os.path.dirname(__file__)
        frontend_path = os.path.join(current_dir, "../frontend")
        backend_uploads_path = os.path.join(current_dir, BACKEND_UPLOADS_PATH)
        images_dir = os.path.join(current_dir, BACKEND_UPLOADS_PATH, 'images')
        videos_dir = os.path.join(current_dir, BACKEND_UPLOADS_PATH, 'videos')

        result = {
            'current_dir': current_dir,
            'frontend_path': os.path.normpath(frontend_path),
            'backend_uploads_path': os.path.normpath(backend_uploads_path),
            'images_dir': os.path.normpath(images_dir),
            'videos_dir': os.path.normpath(videos_dir),
            'images_exists': os.path.exists(images_dir),
            'videos_exists': os.path.exists(videos_dir),
            'image_files': [],
            'video_files': []
        }

        if os.path.exists(images_dir):
            result['image_files'] = [f for f in os.listdir(images_dir)
                                   if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp'))]

        if os.path.exists(videos_dir):
            result['video_files'] = [f for f in os.listdir(videos_dir)
                                   if f.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'))]

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/images')
def images():
    """图片管理页面"""
    # 获取图片列表
    image_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'images')
    image_dir = os.path.normpath(image_dir)
    images = []
    
    if os.path.exists(image_dir):
        for filename in os.listdir(image_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                images.append({
                    'filename': filename,
                    'path': f"/images/{filename}",
                    'size': os.path.getsize(os.path.join(image_dir, filename))
                })
    
    return render_template('images.html', images=images)

@app.route('/content')
def content():
    """内容管理页面"""
    data = load_miniprogram_data()
    return render_template('content.html', data=data)

@app.route('/settings')
def settings():
    """设置页面"""
    data = load_miniprogram_data()
    return render_template('settings.html', data=data)

@app.route('/videos')
def videos():
    """视频管理页面"""
    # 获取视频列表
    video_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'videos')
    video_dir = os.path.normpath(video_dir)
    videos = []

    if os.path.exists(video_dir):
        for filename in os.listdir(video_dir):
            if filename.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv')):
                videos.append({
                    'filename': filename,
                    'path': f"/videos/{filename}",
                    'size': os.path.getsize(os.path.join(video_dir, filename))
                })

    return render_template('videos.html', videos=videos)

@app.route('/video-test')
def video_test():
    """视频播放测试页面"""
    return render_template('video_test.html')

@app.route('/static-test')
def static_test():
    """静态文件测试页面"""
    return render_template('static_test.html')

@app.route('/image-manager')
def image_manager():
    """图片管理页面"""
    return render_template('image_manager.html')

@app.route('/products')
def products():
    """商品管理页面"""
    data = load_miniprogram_data()
    products = data.get('products_data', {}).get('products', [])
    return render_template('products.html', products=products)

@app.route('/culture')
def culture():
    """文化内容管理页面"""
    data = load_miniprogram_data()
    articles = data.get('culture_data', {}).get('articles', [])
    return render_template('culture.html', articles=articles)

# API路由
@app.route('/api/upload', methods=['POST'])
def upload_file():
    """上传文件API - 支持单个或多个文件上传"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有选择文件'})

        files = request.files.getlist('file')
        if not files or all(file.filename == '' for file in files):
            return jsonify({'success': False, 'message': '没有选择文件'})

        uploaded_files = []
        failed_files = []

        # 确保目标目录存在
        images_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'images')
        images_dir = os.path.normpath(images_dir)
        os.makedirs(images_dir, exist_ok=True)

        for file in files:
            if file.filename == '':
                continue

            if file and allowed_file(file.filename):
                try:
                    filename = secure_filename(file.filename)
                    # 生成唯一文件名
                    name, ext = os.path.splitext(filename)
                    unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"

                    # 保存到小程序images目录
                    save_path = os.path.join(images_dir, unique_filename)
                    file.save(save_path)

                    uploaded_files.append({
                        'original_name': filename,
                        'filename': unique_filename,
                        'path': f"/images/{unique_filename}"
                    })
                except Exception as e:
                    failed_files.append({
                        'filename': file.filename,
                        'error': str(e)
                    })
            else:
                failed_files.append({
                    'filename': file.filename,
                    'error': '文件格式不支持'
                })

        if uploaded_files:
            message = f"成功上传 {len(uploaded_files)} 个文件"
            if failed_files:
                message += f"，{len(failed_files)} 个文件上传失败"

            return jsonify({
                'success': True,
                'message': message,
                'uploaded_files': uploaded_files,
                'failed_files': failed_files
            })
        else:
            return jsonify({
                'success': False,
                'message': '所有文件上传失败',
                'failed_files': failed_files
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传过程中发生错误: {str(e)}'
        })

@app.route('/api/upload_video', methods=['POST'])
def upload_video():
    """上传视频文件API"""
    try:
        if 'video' not in request.files:
            return jsonify({'success': False, 'message': '没有选择视频文件'})

        files = request.files.getlist('video')
        if not files or all(file.filename == '' for file in files):
            return jsonify({'success': False, 'message': '没有选择视频文件'})

        uploaded_files = []
        failed_files = []

        # 确保目标目录存在
        videos_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'videos')
        videos_dir = os.path.normpath(videos_dir)
        os.makedirs(videos_dir, exist_ok=True)

        for file in files:
            if file.filename == '':
                continue

            if file and allowed_video_file(file.filename):
                try:
                    # 检查文件大小
                    file.seek(0, 2)  # 移动到文件末尾
                    file_size = file.tell()
                    file.seek(0)  # 重置到文件开头

                    if file_size > MAX_VIDEO_SIZE:
                        failed_files.append({
                            'filename': file.filename,
                            'error': f'文件过大 ({file_size / 1024 / 1024:.1f}MB > 100MB)'
                        })
                        continue

                    filename = secure_filename(file.filename)
                    # 生成唯一文件名
                    name, ext = os.path.splitext(filename)
                    unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"

                    # 保存到小程序videos目录
                    save_path = os.path.join(videos_dir, unique_filename)
                    file.save(save_path)

                    uploaded_files.append({
                        'original_name': filename,
                        'filename': unique_filename,
                        'path': f"/videos/{unique_filename}"
                    })
                except Exception as e:
                    failed_files.append({
                        'filename': file.filename,
                        'error': str(e)
                    })
            else:
                failed_files.append({
                    'filename': file.filename,
                    'error': '视频格式不支持'
                })

        if uploaded_files:
            # 自动更新视频配置
            try:
                update_video_config()
            except Exception as e:
                print(f"更新视频配置失败: {e}")

            message = f"成功上传 {len(uploaded_files)} 个视频文件"
            if failed_files:
                message += f"，{len(failed_files)} 个文件上传失败"

            return jsonify({
                'success': True,
                'message': message,
                'uploaded_files': uploaded_files,
                'failed_files': failed_files
            })
        else:
            return jsonify({
                'success': False,
                'message': '所有视频文件上传失败',
                'failed_files': failed_files
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传过程中发生错误: {str(e)}'
        })

@app.route('/api/save_data', methods=['POST'])
def save_data():
    """保存数据API"""
    try:
        data = request.get_json()
        if save_miniprogram_data(data):
            sync_to_miniprogram()
            return jsonify({'success': True, 'message': '保存成功'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})

@app.route('/api/delete_image', methods=['POST'])
def delete_image():
    """删除图片API"""
    try:
        filename = request.json.get('filename')
        if not filename:
            return jsonify({'success': False, 'message': '文件名不能为空'})

        file_path = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'images', filename)
        file_path = os.path.normpath(file_path)
        if os.path.exists(file_path):
            os.remove(file_path)
            return jsonify({'success': True, 'message': '删除成功'})
        else:
            return jsonify({'success': False, 'message': '文件不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@app.route('/api/delete_video', methods=['POST'])
def delete_video():
    """删除视频API"""
    try:
        filename = request.json.get('filename')
        if not filename:
            return jsonify({'success': False, 'message': '文件名不能为空'})

        file_path = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'videos', filename)
        file_path = os.path.normpath(file_path)
        if os.path.exists(file_path):
            os.remove(file_path)
            # 删除后自动更新配置
            try:
                update_video_config()
            except Exception as e:
                print(f"更新视频配置失败: {e}")
            return jsonify({'success': True, 'message': '视频删除成功'})
        else:
            return jsonify({'success': False, 'message': '视频文件不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})


@app.route('/api/list_images')
def list_images():
    """获取图片列表API"""
    try:
        images_dir = os.path.join(os.path.dirname(__file__), BACKEND_UPLOADS_PATH, 'images')
        images_dir = os.path.normpath(images_dir)

        if not os.path.exists(images_dir):
            return jsonify({'success': True, 'images': []})

        images = []
        for filename in os.listdir(images_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                file_path = os.path.join(images_dir, filename)
                file_size = os.path.getsize(file_path)

                images.append({
                    'filename': filename,
                    'url': f'http://127.0.0.1:5000/images/{filename}',
                    'local_path': f'/images/{filename}',
                    'size': file_size,
                    'modified': os.path.getmtime(file_path)
                })

        # 按修改时间排序，最新的在前
        images.sort(key=lambda x: x['modified'], reverse=True)

        return jsonify({
            'success': True,
            'images': images,
            'total': len(images)
        })

    except Exception as e:
        print(f"获取图片列表失败: {e}")
        return jsonify({'success': False, 'message': f'获取图片列表失败: {str(e)}'})

@app.route('/api/refresh_videos', methods=['POST'])
def refresh_videos():
    """刷新视频配置API"""
    try:
        update_video_config()
        return jsonify({'success': True, 'message': '视频配置已刷新'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'刷新失败: {str(e)}'})

@app.route('/api/fix_video_config', methods=['POST'])
def fix_video_config():
    """修复视频配置API - 确保使用网络地址和正确的视频源"""
    try:
        data = load_miniprogram_data()
        fixed_count = 0

        # 修复视频配置，确保使用网络地址
        if 'home_data' in data and 'video_list' in data['home_data']:
            for video in data['home_data']['video_list']:
                # 确保thumbnail使用网络地址
                if 'thumbnail' in video and not video['thumbnail'].startswith('http'):
                    if video['thumbnail'].startswith('/'):
                        video['thumbnail'] = f"http://127.0.0.1:5000{video['thumbnail']}"
                    else:
                        video['thumbnail'] = f"http://127.0.0.1:5000/images/{video['thumbnail']}"
                    fixed_count += 1

                # 修复视频URL，确保使用网络地址
                if 'url' in video:
                    url = video['url']
                    if url and not url.startswith('http'):
                        if url.startswith('/videos/'):
                            # 本地视频文件，转换为网络地址
                            video['url'] = f"http://127.0.0.1:5000{url}"
                            fixed_count += 1
                        elif url.startswith('/'):
                            # 其他本地路径
                            video['url'] = f"http://127.0.0.1:5000{url}"
                            fixed_count += 1
                else:
                    # 没有url字段，使用测试视频
                    test_urls = [
                        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4"
                    ]
                    video_id = video.get('id', 1)
                    video['url'] = test_urls[(video_id - 1) % len(test_urls)]
                    fixed_count += 1

                # 确保有showPlayer字段
                if 'showPlayer' not in video:
                    video['showPlayer'] = False
                    fixed_count += 1

        # 保存修复后的数据
        if fixed_count > 0:
            if save_miniprogram_data(data):
                return jsonify({'success': True, 'message': f'视频配置已修复，共修复 {fixed_count} 项'})
            else:
                return jsonify({'success': False, 'message': '保存失败'})
        else:
            return jsonify({'success': True, 'message': '视频配置无需修复'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'修复失败: {str(e)}'})

@app.route('/api/fix_image_paths', methods=['POST'])
def fix_image_paths():
    """修复图片和视频路径API - 将本地路径转换为网络地址"""
    try:
        data = load_miniprogram_data()
        fixed_count = 0
        image_fixed = 0
        video_fixed = 0

        # 修复指南数据中的图片路径
        if 'guide_data' in data.get('home_data', {}):
            for guide in data['home_data']['guide_data']:
                if 'cover_image' in guide:
                    old_path = guide['cover_image']
                    if old_path and not old_path.startswith('http'):
                        if old_path.startswith('/'):
                            guide['cover_image'] = f"http://127.0.0.1:5000{old_path}"
                        else:
                            guide['cover_image'] = f"http://127.0.0.1:5000/images/{old_path}"
                        fixed_count += 1
                        image_fixed += 1

        # 修复视频数据中的缩略图和视频URL路径
        if 'video_list' in data.get('home_data', {}):
            for video in data['home_data']['video_list']:
                # 修复缩略图路径
                if 'thumbnail' in video:
                    old_path = video['thumbnail']
                    if old_path and not old_path.startswith('http'):
                        if old_path.startswith('/'):
                            video['thumbnail'] = f"http://127.0.0.1:5000{old_path}"
                        else:
                            video['thumbnail'] = f"http://127.0.0.1:5000/images/{old_path}"
                        fixed_count += 1
                        image_fixed += 1

                # 修复视频URL路径
                if 'url' in video:
                    old_url = video['url']
                    if old_url and not old_url.startswith('http'):
                        if old_url.startswith('/videos/'):
                            video['url'] = f"http://127.0.0.1:5000{old_url}"
                            fixed_count += 1
                            video_fixed += 1
                        elif old_url.startswith('/'):
                            video['url'] = f"http://127.0.0.1:5000{old_url}"
                            fixed_count += 1
                            video_fixed += 1

                # 确保有showPlayer字段
                if 'showPlayer' not in video:
                    video['showPlayer'] = False
                    fixed_count += 1

        # 修复推荐商品中的图片路径
        if 'recommend_products' in data.get('home_data', {}):
            for product in data['home_data']['recommend_products']:
                if 'image' in product:
                    old_path = product['image']
                    if old_path and not old_path.startswith('http'):
                        if old_path.startswith('/'):
                            product['image'] = f"http://127.0.0.1:5000{old_path}"
                        else:
                            product['image'] = f"http://127.0.0.1:5000/images/{old_path}"
                        fixed_count += 1
                        image_fixed += 1

        # 修复用户头像路径
        if 'user_data' in data and 'default_user' in data['user_data']:
            user = data['user_data']['default_user']
            if 'avatar_url' in user:
                old_path = user['avatar_url']
                if old_path and not old_path.startswith('http'):
                    if old_path.startswith('/'):
                        user['avatar_url'] = f"http://127.0.0.1:5000{old_path}"
                    else:
                        user['avatar_url'] = f"http://127.0.0.1:5000/images/{old_path}"
                    fixed_count += 1
                    image_fixed += 1

        # 修复文化数据中的图片路径
        if 'culture_data' in data and 'articles' in data['culture_data']:
            for article in data['culture_data']['articles']:
                # 修复封面图片
                if 'cover_image' in article:
                    old_path = article['cover_image']
                    if old_path and not old_path.startswith('http'):
                        if old_path.startswith('/'):
                            article['cover_image'] = f"http://127.0.0.1:5000{old_path}"
                        else:
                            article['cover_image'] = f"http://127.0.0.1:5000/images/{old_path}"
                        fixed_count += 1
                        image_fixed += 1

                # 修复文章内图片
                if 'images' in article and isinstance(article['images'], list):
                    for i, img_path in enumerate(article['images']):
                        if img_path and not img_path.startswith('http'):
                            if img_path.startswith('/'):
                                article['images'][i] = f"http://127.0.0.1:5000{img_path}"
                            else:
                                article['images'][i] = f"http://127.0.0.1:5000/images/{img_path}"
                            fixed_count += 1
                            image_fixed += 1

        # 保存修复后的数据
        if fixed_count > 0:
            save_miniprogram_data(data)
            message_parts = []
            if image_fixed > 0:
                message_parts.append(f'{image_fixed} 个图片路径')
            if video_fixed > 0:
                message_parts.append(f'{video_fixed} 个视频路径')

            message = f"已修复 {' 和 '.join(message_parts)}"
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': True, 'message': '所有图片和视频路径都是正确的'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'修复失败: {str(e)}'})

# ==================== 商品管理API ====================

@app.route('/api/get_product/<int:product_id>')
def get_product(product_id):
    """获取单个商品信息"""
    try:
        data = load_miniprogram_data()
        products = data.get('products_data', {}).get('products', [])

        product = next((p for p in products if p['id'] == product_id), None)
        if product:
            return jsonify({'success': True, 'data': product})
        else:
            return jsonify({'success': False, 'message': '商品不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取商品失败: {str(e)}'})

@app.route('/api/add_product', methods=['POST'])
def add_product():
    """添加商品"""
    try:
        product_data = request.json
        data = load_miniprogram_data()

        # 确保products_data存在
        if 'products_data' not in data:
            data['products_data'] = {'products': []}
        if 'products' not in data['products_data']:
            data['products_data']['products'] = []

        products = data['products_data']['products']

        # 生成新的ID
        new_id = max([p['id'] for p in products], default=0) + 1

        # 构建商品数据
        new_product = {
            'id': new_id,
            'name': product_data.get('name', ''),
            'price': product_data.get('price', '0.00'),
            'original_price': product_data.get('original_price', ''),
            'image': product_data.get('image', ''),
            'images': product_data.get('images', []),  # 轮播图片数组
            'category': product_data.get('category', ''),
            'description': product_data.get('description', ''),
            'stock': int(product_data.get('stock', 0)),
            'sales': int(product_data.get('sales', 0)),
            'rating': 5.0,
            'reviews': 0,
            'tags': [],
            'status': product_data.get('status', 'active'),
            'detail': {
                'materials': product_data.get('materials', ''),
                'size': product_data.get('size', ''),
                'weight': product_data.get('weight', ''),
                'origin': product_data.get('origin', ''),
                'craft': product_data.get('craft', ''),
                'features': product_data.get('features', []),
                'story': product_data.get('story', '')
            }
        }

        products.append(new_product)

        # 同时更新首页推荐商品
        if 'home_data' not in data:
            data['home_data'] = {}
        if 'recommend_products' not in data['home_data']:
            data['home_data']['recommend_products'] = []

        # 如果推荐商品少于4个，自动添加到推荐
        if len(data['home_data']['recommend_products']) < 4:
            recommend_product = {
                'id': new_id,
                'name': new_product['name'],
                'price': new_product['price'],
                'original_price': new_product['original_price'],
                'image': new_product['image'],
                'category': new_product['category']
            }
            data['home_data']['recommend_products'].append(recommend_product)

        if save_miniprogram_data(data):
            return jsonify({'success': True, 'message': '商品添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'添加商品失败: {str(e)}'})

@app.route('/api/update_product', methods=['POST'])
def update_product():
    """更新商品"""
    try:
        product_data = request.json
        data = load_miniprogram_data()

        products = data.get('products_data', {}).get('products', [])
        product_id = int(product_data.get('id'))

        # 找到要更新的商品
        product_index = next((i for i, p in enumerate(products) if p['id'] == product_id), None)
        if product_index is None:
            return jsonify({'success': False, 'message': '商品不存在'})

        # 更新商品数据
        products[product_index].update({
            'name': product_data.get('name', ''),
            'price': product_data.get('price', '0.00'),
            'original_price': product_data.get('original_price', ''),
            'image': product_data.get('image', ''),
            'images': product_data.get('images', []),  # 轮播图片数组
            'category': product_data.get('category', ''),
            'description': product_data.get('description', ''),
            'stock': int(product_data.get('stock', 0)),
            'sales': int(product_data.get('sales', 0)),
            'status': product_data.get('status', 'active'),
            'detail': {
                'materials': product_data.get('materials', ''),
                'size': product_data.get('size', ''),
                'weight': product_data.get('weight', ''),
                'origin': product_data.get('origin', ''),
                'craft': product_data.get('craft', ''),
                'features': product_data.get('features', []),
                'story': product_data.get('story', '')
            }
        })

        # 同时更新首页推荐商品
        recommend_products = data.get('home_data', {}).get('recommend_products', [])
        recommend_index = next((i for i, p in enumerate(recommend_products) if p['id'] == product_id), None)
        if recommend_index is not None:
            recommend_products[recommend_index].update({
                'name': products[product_index]['name'],
                'price': products[product_index]['price'],
                'original_price': products[product_index]['original_price'],
                'image': products[product_index]['image'],
                'category': products[product_index]['category']
            })

        if save_miniprogram_data(data):
            return jsonify({'success': True, 'message': '商品更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'更新商品失败: {str(e)}'})

@app.route('/api/delete_product', methods=['POST'])
def delete_product():
    """删除商品"""
    try:
        product_id = int(request.json.get('id'))
        data = load_miniprogram_data()

        products = data.get('products_data', {}).get('products', [])

        # 删除商品
        data['products_data']['products'] = [p for p in products if p['id'] != product_id]

        # 同时从推荐商品中删除
        if 'home_data' in data and 'recommend_products' in data['home_data']:
            data['home_data']['recommend_products'] = [
                p for p in data['home_data']['recommend_products'] if p['id'] != product_id
            ]

        if save_miniprogram_data(data):
            return jsonify({'success': True, 'message': '商品删除成功'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'删除商品失败: {str(e)}'})

@app.route('/api/reset_data', methods=['POST'])
def reset_data():
    """重置数据到默认状态"""
    try:
        # 获取默认数据
        default_data = get_default_data()
        if not default_data:
            return jsonify({'success': False, 'message': '默认数据不可用'})

        # 保存默认数据
        if save_miniprogram_data(default_data):
            # 同步到小程序
            sync_success = sync_to_miniprogram()
            return jsonify({
                'success': True,
                'message': '数据重置成功，已恢复到默认演示状态',
                'sync_success': sync_success
            })
        else:
            return jsonify({'success': False, 'message': '重置失败'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'重置失败: {str(e)}'})

@app.route('/api/save_home_content', methods=['POST'])
def save_home_content():
    """保存首页内容API"""
    try:
        form_data = request.get_json()
        data = load_miniprogram_data()

        # 更新指南数据
        guide_data = []
        guide_ids = set()
        for key in form_data.keys():
            if key.startswith('guide_title_'):
                guide_id = key.split('_')[-1]
                guide_ids.add(guide_id)

        for guide_id in sorted(guide_ids, key=int):
            # 处理图片路径，确保使用网络地址
            cover_image = form_data.get(f'guide_image_{guide_id}', '')
            if cover_image and not cover_image.startswith('http'):
                if cover_image.startswith('/'):
                    cover_image = f"http://127.0.0.1:5000{cover_image}"
                else:
                    cover_image = f"http://127.0.0.1:5000/images/{cover_image}"

            guide_data.append({
                'id': int(guide_id) + 1,
                'title': form_data.get(f'guide_title_{guide_id}', ''),
                'expert_name': form_data.get(f'guide_expert_{guide_id}', ''),
                'publish_date': form_data.get(f'guide_date_{guide_id}', ''),
                'cover_image': cover_image,
                'description': form_data.get(f'guide_description_{guide_id}', '')
            })
        data['home_data']['guide_data'] = guide_data

        # 更新快捷入口
        quick_actions = []
        action_ids = set()
        for key in form_data.keys():
            if key.startswith('action_icon_'):
                action_id = key.split('_')[-1]
                action_ids.add(action_id)

        for action_id in action_ids:
            quick_actions.append({
                'id': int(action_id),
                'icon': form_data.get(f'action_icon_{action_id}', ''),
                'text': form_data.get(f'action_text_{action_id}', ''),
                'action': form_data.get(f'action_action_{action_id}', '')
            })
        data['home_data']['quick_actions'] = quick_actions

        # 更新视频列表
        video_list = []
        video_ids = set()
        for key in form_data.keys():
            if key.startswith('video_title_'):
                video_id = key.split('_')[-1]
                video_ids.add(video_id)

        for video_id in video_ids:
            video_list.append({
                'id': int(video_id),
                'title': form_data.get(f'video_title_{video_id}', ''),
                'thumbnail': form_data.get(f'video_thumbnail_{video_id}', ''),
                'duration': form_data.get(f'video_duration_{video_id}', ''),
                'url': form_data.get(f'video_url_{video_id}', ''),
                'showPlayer': False
            })
        data['home_data']['video_list'] = video_list

        # 更新时间线数据
        timeline_data = []
        timeline_ids = set()
        for key in form_data.keys():
            if key.startswith('timeline_year_'):
                timeline_id = key.split('_')[-1]
                timeline_ids.add(timeline_id)

        for timeline_id in timeline_ids:
            timeline_data.append({
                'id': int(timeline_id),
                'year': form_data.get(f'timeline_year_{timeline_id}', ''),
                'description': form_data.get(f'timeline_desc_{timeline_id}', '')
            })
        data['home_data']['timeline_data'] = timeline_data

        if save_miniprogram_data(data):
            # 自动同步到小程序
            sync_success = sync_to_miniprogram()

            # 返回成功响应，包含刷新标记
            response_data = {
                'success': True,
                'message': '首页内容保存成功',
                'sync_success': sync_success,
                'refresh_required': True  # 标记需要刷新
            }

            if sync_success:
                response_data['message'] = '首页内容保存并同步成功'
            else:
                response_data['message'] = '首页内容保存成功，但样式同步失败'

            return jsonify(response_data)
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})

@app.route('/api/save_category_content', methods=['POST'])
def save_category_content():
    """保存分类内容API"""
    try:
        form_data = request.get_json()
        data = load_miniprogram_data()

        # 更新分类数据
        categories = []
        category_ids = set()
        for key in form_data.keys():
            if key.startswith('category_name_'):
                category_id = key.split('_')[-1]
                category_ids.add(category_id)

        for category_id in category_ids:
            categories.append({
                'id': int(category_id),
                'name': form_data.get(f'category_name_{category_id}', ''),
                'count': int(form_data.get(f'category_count_{category_id}', 0)),
                'category': form_data.get(f'category_key_{category_id}', '')
            })
        data['category_data']['categories'] = categories

        if save_miniprogram_data(data):
            # 自动同步到小程序
            sync_success = sync_to_miniprogram()
            if sync_success:
                return jsonify({'success': True, 'message': '分类设置保存并同步成功'})
            else:
                return jsonify({'success': True, 'message': '分类设置保存成功，但同步失败'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})

@app.route('/api/save_user_content', methods=['POST'])
def save_user_content():
    """保存用户内容API"""
    try:
        form_data = request.get_json()
        data = load_miniprogram_data()

        # 更新用户数据
        data['user_data']['default_user'] = {
            'nick_name': form_data.get('user_nickname', ''),
            'avatar_url': form_data.get('user_avatar', ''),
            'phone': form_data.get('user_phone', ''),
            'level': form_data.get('user_level', 'VIP'),
            'points': int(form_data.get('user_points', 0))
        }

        data['user_data']['assets'] = {
            'coupons': int(form_data.get('user_coupons', 0)),
            'points': int(form_data.get('user_points', 0)),
            'balance': form_data.get('user_balance', '0.00'),
            'cards': int(form_data.get('user_cards', 0))
        }

        if save_miniprogram_data(data):
            # 自动同步到小程序
            sync_success = sync_to_miniprogram()
            if sync_success:
                return jsonify({'success': True, 'message': '用户设置保存并同步成功'})
            else:
                return jsonify({'success': True, 'message': '用户设置保存成功，但同步失败'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})

@app.route('/api/save_app_config', methods=['POST'])
def save_app_config():
    """保存应用配置API"""
    try:
        config_data = request.get_json()
        data = load_miniprogram_data()

        data['app_config'].update(config_data)

        if save_miniprogram_data(data):
            # 自动同步到小程序
            sync_success = sync_to_miniprogram()
            if sync_success:
                return jsonify({'success': True, 'message': '应用配置保存并同步成功'})
            else:
                return jsonify({'success': True, 'message': '应用配置保存成功，但同步失败'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})

@app.route('/api/export_data')
def export_data():
    """导出数据API"""
    try:
        data = load_miniprogram_data()
        return jsonify(data)
    except Exception as e:
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'})

@app.route('/api/miniprogram/data')
def get_miniprogram_data():
    """小程序数据API - 专门为小程序提供数据"""
    try:
        data = load_miniprogram_data()
        return jsonify({
            'success': True,
            'data': data,
            'message': '数据获取成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'数据获取失败: {str(e)}'
        })

@app.route('/api/miniprogram/home')
def get_home_data():
    """获取首页数据API"""
    try:
        data = load_miniprogram_data()
        home_data = data.get('home_data', {})
        return jsonify({
            'success': True,
            'data': home_data,
            'message': '首页数据获取成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'首页数据获取失败: {str(e)}'
        })

@app.route('/api/miniprogram/category')
def get_category_data():
    """获取分类数据API"""
    try:
        data = load_miniprogram_data()
        category_data = data.get('category_data', {})
        return jsonify({
            'success': True,
            'data': category_data,
            'message': '分类数据获取成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'分类数据获取失败: {str(e)}'
        })

@app.route('/api/miniprogram/user')
def get_user_data():
    """获取用户数据API"""
    try:
        data = load_miniprogram_data()
        user_data = data.get('user_data', {})
        return jsonify({
            'success': True,
            'data': user_data,
            'message': '用户数据获取成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'用户数据获取失败: {str(e)}'
        })

@app.route('/api/miniprogram/config')
def get_app_config():
    """获取应用配置API"""
    try:
        data = load_miniprogram_data()
        app_config = data.get('app_config', {})
        return jsonify({
            'success': True,
            'data': app_config,
            'message': '应用配置获取成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'应用配置获取失败: {str(e)}'
        })

@app.route('/api/miniprogram/culture')
def get_culture_data():
    """获取文化数据API"""
    try:
        data = load_miniprogram_data()
        culture_data = data.get('culture_data', {})
        return jsonify({
            'success': True,
            'data': culture_data,
            'message': '文化数据获取成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'data': None,
            'message': f'文化数据获取失败: {str(e)}'
        })

# ==================== 文章管理API ====================

@app.route('/api/get_article/<int:article_id>')
def get_article(article_id):
    """获取单个文章信息"""
    try:
        data = load_miniprogram_data()
        articles = data.get('culture_data', {}).get('articles', [])

        article = next((a for a in articles if a['id'] == article_id), None)
        if article:
            return jsonify({'success': True, 'data': article})
        else:
            return jsonify({'success': False, 'message': '文章不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取文章失败: {str(e)}'})

@app.route('/api/add_article', methods=['POST'])
def add_article():
    """添加文章"""
    try:
        article_data = request.get_json()
        data = load_miniprogram_data()

        if 'culture_data' not in data:
            data['culture_data'] = {'articles': []}
        if 'articles' not in data['culture_data']:
            data['culture_data']['articles'] = []

        articles = data['culture_data']['articles']

        # 生成新的ID
        new_id = max([a['id'] for a in articles], default=0) + 1
        article_data['id'] = new_id

        # 设置默认值
        article_data.setdefault('views', 0)
        article_data.setdefault('likes', 0)
        article_data.setdefault('status', 'published')

        articles.append(article_data)

        if save_miniprogram_data(data):
            return jsonify({'success': True, 'message': '文章添加成功'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'添加文章失败: {str(e)}'})

@app.route('/api/update_article', methods=['POST'])
def update_article():
    """更新文章"""
    try:
        article_data = request.get_json()
        data = load_miniprogram_data()

        articles = data.get('culture_data', {}).get('articles', [])
        article_id = article_data.get('id')

        for i, article in enumerate(articles):
            if article['id'] == article_id:
                articles[i].update(article_data)
                break
        else:
            return jsonify({'success': False, 'message': '文章不存在'})

        if save_miniprogram_data(data):
            return jsonify({'success': True, 'message': '文章更新成功'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'更新文章失败: {str(e)}'})

@app.route('/api/delete_article', methods=['POST'])
def delete_article():
    """删除文章"""
    try:
        request_data = request.get_json()
        article_id = request_data.get('id')

        data = load_miniprogram_data()
        articles = data.get('culture_data', {}).get('articles', [])

        # 找到并删除文章
        for i, article in enumerate(articles):
            if article['id'] == article_id:
                articles.pop(i)
                break
        else:
            return jsonify({'success': False, 'message': '文章不存在'})

        if save_miniprogram_data(data):
            return jsonify({'success': True, 'message': '文章删除成功'})
        else:
            return jsonify({'success': False, 'message': '保存失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'删除文章失败: {str(e)}'})

@app.route('/api/import_data', methods=['POST'])
def import_data():
    """导入数据API"""
    try:
        data = request.get_json()
        if save_miniprogram_data(data):
            return jsonify({'success': True, 'message': '数据导入成功'})
        else:
            return jsonify({'success': False, 'message': '导入失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'导入失败: {str(e)}'})

@app.route('/api/sync_data', methods=['POST'])
def sync_data():
    """手动同步数据到小程序API"""
    try:
        success = sync_to_miniprogram()
        if success:
            return jsonify({'success': True, 'message': '数据同步成功'})
        else:
            return jsonify({'success': False, 'message': '同步失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'同步失败: {str(e)}'})

if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('data', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)

    # 启动应用
    app.run(debug=True, host='0.0.0.0', port=5000)
