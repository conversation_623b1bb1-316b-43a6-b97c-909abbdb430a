// utils/url-mapper.js
// URL映射工具 - 智能处理后端传来的localhost地址

const envConfig = require('../config/env.js');

/**
 * URL映射器类
 */
class UrlMapper {
  constructor() {
    // 需要映射的localhost地址模式
    this.localhostPatterns = [
      'http://localhost:5000',
      'http://127.0.0.1:5000',
      'https://localhost:5000',
      'https://127.0.0.1:5000',
      'http://localhost:8000',
      'http://127.0.0.1:8000',
      'https://localhost:8000',
      'https://127.0.0.1:8000'
    ];
  }

  /**
   * 映射单个URL
   * @param {string} url 原始URL
   * @returns {string} 映射后的URL
   */
  mapUrl(url) {
    if (!url || typeof url !== 'string') return url;
    
    // 如果不是HTTP URL，直接返回
    if (!url.startsWith('http')) return url;
    
    const currentConfig = envConfig.getCurrentEnvConfig();
    
    // 检查是否需要映射
    for (const pattern of this.localhostPatterns) {
      if (url.startsWith(pattern)) {
        const mappedUrl = url.replace(pattern, currentConfig.baseUrl);
        console.log(`[URL映射] ${url} -> ${mappedUrl}`);
        return mappedUrl;
      }
    }
    
    // 不需要映射，返回原URL
    return url;
  }

  /**
   * 映射图片URL
   * @param {string} imageUrl 图片URL
   * @returns {string} 映射后的图片URL
   */
  mapImageUrl(imageUrl) {
    if (!imageUrl) return '';
    
    // 如果是完整URL，进行映射
    if (imageUrl.startsWith('http')) {
      return this.mapUrl(imageUrl);
    }
    
    // 如果是相对路径，使用环境配置构建
    return envConfig.getImageUrl(imageUrl);
  }

  /**
   * 映射视频URL
   * @param {string} videoUrl 视频URL
   * @returns {string} 映射后的视频URL
   */
  mapVideoUrl(videoUrl) {
    if (!videoUrl) return '';
    
    // 如果是完整URL，进行映射
    if (videoUrl.startsWith('http')) {
      return this.mapUrl(videoUrl);
    }
    
    // 如果是相对路径，使用环境配置构建
    return envConfig.getVideoUrl(videoUrl);
  }

  /**
   * 递归映射对象中的所有URL
   * @param {any} data 数据对象
   * @param {Array} urlFields 需要映射的字段名列表
   * @returns {any} 映射后的数据
   */
  mapDataUrls(data, urlFields = ['url', 'image', 'cover_image', 'thumbnail', 'avatar', 'avatarUrl']) {
    if (!data) return data;
    
    const mapObject = (obj) => {
      if (Array.isArray(obj)) {
        return obj.map(item => mapObject(item));
      } else if (obj && typeof obj === 'object') {
        const newObj = {};
        for (const key in obj) {
          if (urlFields.includes(key) && typeof obj[key] === 'string') {
            // 映射URL字段
            newObj[key] = this.mapUrl(obj[key]);
          } else {
            // 递归处理其他字段
            newObj[key] = mapObject(obj[key]);
          }
        }
        return newObj;
      }
      return obj;
    };
    
    return mapObject(data);
  }

  /**
   * 映射首页数据中的URL
   * @param {Object} homeData 首页数据
   * @returns {Object} 映射后的首页数据
   */
  mapHomeData(homeData) {
    if (!homeData) return homeData;
    
    const mappedData = { ...homeData };
    
    // 映射指南数据中的封面图片
    if (mappedData.guideData && Array.isArray(mappedData.guideData)) {
      mappedData.guideData = mappedData.guideData.map(guide => ({
        ...guide,
        cover_image: this.mapImageUrl(guide.cover_image)
      }));
    }
    
    // 映射视频列表中的缩略图和视频URL
    if (mappedData.videoList && Array.isArray(mappedData.videoList)) {
      mappedData.videoList = mappedData.videoList.map(video => ({
        ...video,
        thumbnail: this.mapImageUrl(video.thumbnail),
        url: this.mapVideoUrl(video.url)
      }));
    }
    
    // 映射推荐商品中的图片
    if (mappedData.recommendProducts && Array.isArray(mappedData.recommendProducts)) {
      mappedData.recommendProducts = mappedData.recommendProducts.map(product => ({
        ...product,
        image: this.mapImageUrl(product.image)
      }));
    }
    
    // 映射时间线数据中的图片
    if (mappedData.timelineData && Array.isArray(mappedData.timelineData)) {
      mappedData.timelineData = mappedData.timelineData.map(item => ({
        ...item,
        image: this.mapImageUrl(item.image)
      }));
    }
    
    return mappedData;
  }

  /**
   * 映射用户数据中的URL
   * @param {Object} userData 用户数据
   * @returns {Object} 映射后的用户数据
   */
  mapUserData(userData) {
    if (!userData) return userData;
    
    const mappedData = { ...userData };
    
    // 映射用户头像
    if (mappedData.userInfo && mappedData.userInfo.avatarUrl) {
      mappedData.userInfo.avatarUrl = this.mapImageUrl(mappedData.userInfo.avatarUrl);
    }
    
    return mappedData;
  }

  /**
   * 添加自定义localhost模式
   * @param {string} pattern localhost模式
   */
  addLocalhostPattern(pattern) {
    if (pattern && !this.localhostPatterns.includes(pattern)) {
      this.localhostPatterns.push(pattern);
    }
  }

  /**
   * 获取当前映射配置信息
   * @returns {Object} 配置信息
   */
  getMapperInfo() {
    const currentConfig = envConfig.getCurrentEnvConfig();
    return {
      currentEnvironment: envConfig.ENV_CONFIG.current,
      targetBaseUrl: currentConfig.baseUrl,
      localhostPatterns: this.localhostPatterns,
      mappingEnabled: currentConfig.baseUrl !== 'http://localhost:5000' && 
                     currentConfig.baseUrl !== 'http://127.0.0.1:5000'
    };
  }
}

// 创建全局实例
const urlMapper = new UrlMapper();

// 导出实例和类
module.exports = {
  urlMapper,
  UrlMapper
};
