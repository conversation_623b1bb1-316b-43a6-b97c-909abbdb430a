// config/env.js
// 环境配置文件 - 统一管理不同环境的配置

/**
 * 环境配置
 * 部署时只需要修改这个文件即可
 */
const ENV_CONFIG = {
  // 当前环境：development | production
  // 部署到服务器时改为 'production'
  current: 'development',
  
  // 开发环境配置
  development: {
    name: '开发环境',
    baseUrl: 'http://localhost:5000',
    apiUrl: 'http://localhost:5000/api',
    imageUrl: 'http://localhost:5000/images',
    videoUrl: 'http://localhost:5000/videos',
    uploadUrl: 'http://localhost:5000/api/upload',
    wsUrl: 'ws://localhost:5000/ws', // WebSocket地址（如果需要）
    debug: true
  },
  
  // 生产环境配置
  production: {
    name: '生产环境',
  },
  
  // 测试环境配置（可选）
  testing: {
    name: '测试环境',
    baseUrl: 'http://test.yourdomain.com:5000',
    apiUrl: 'http://test.yourdomain.com:5000/api',
    imageUrl: 'http://test.yourdomain.com:5000/images',
    videoUrl: 'http://test.yourdomain.com:5000/videos',
    uploadUrl: 'http://test.yourdomain.com:5000/api/upload',
    wsUrl: 'ws://test.yourdomain.com:5000/ws',
    debug: true
  }
};

/**
 * 应用配置
 */
const APP_CONFIG = {
  // 应用信息
  appName: '非物质文化遗产·漆器',
  version: '1.0.0',
  description: '传承千年工艺，品味文化之美',
  
  // 请求配置
  timeout: 10000,        // 请求超时时间(毫秒)
  retryCount: 3,         // 重试次数
  cacheTimeout: 30000,   // 缓存超时时间(毫秒) - 30秒
  
  // 上传配置
  maxImageSize: 5 * 1024 * 1024,    // 最大图片大小 5MB
  maxVideoSize: 50 * 1024 * 1024,   // 最大视频大小 50MB
  allowedImageTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  allowedVideoTypes: ['mp4', 'avi', 'mov', 'wmv'],
  
  // 分页配置
  pageSize: 20,          // 默认分页大小
  maxPageSize: 100,      // 最大分页大小
  
  // 缓存配置
  enableCache: true,     // 是否启用缓存
  cacheKeys: {
    home: 'home_data',
    category: 'category_data',
    user: 'user_data',
    config: 'app_config'
  }
};

/**
 * 获取当前环境配置
 * @returns {Object} 当前环境的配置对象
 */
function getCurrentEnvConfig() {
  const currentEnv = ENV_CONFIG.current;
  const envConfig = ENV_CONFIG[currentEnv];
  
  if (!envConfig) {
    console.error(`环境配置错误: ${currentEnv} 不存在`);
    return ENV_CONFIG.development; // 降级到开发环境
  }
  
  return envConfig;
}

/**
 * 获取完整配置
 * @returns {Object} 包含环境配置和应用配置的完整配置对象
 */
function getFullConfig() {
  const envConfig = getCurrentEnvConfig();
  
  return {
    env: ENV_CONFIG.current,
    ...envConfig,
    ...APP_CONFIG,
    // 合并服务器配置到根级别，方便访问
    servers: ENV_CONFIG
  };
}

/**
 * 检查是否为开发环境
 * @returns {boolean}
 */
function isDevelopment() {
  return ENV_CONFIG.current === 'development';
}

/**
 * 检查是否为生产环境
 * @returns {boolean}
 */
function isProduction() {
  return ENV_CONFIG.current === 'production';
}

/**
 * 获取API完整URL
 * @param {string} path API路径
 * @returns {string} 完整的API URL
 */
function getApiUrl(path = '') {
  const config = getCurrentEnvConfig();
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  return cleanPath ? `${config.apiUrl}/${cleanPath}` : config.apiUrl;
}

/**
 * 获取图片完整URL（智能映射）
 * @param {string} imagePath 图片路径
 * @returns {string} 完整的图片URL
 */
function getImageUrl(imagePath = '') {
  if (!imagePath) return '';

  const config = getCurrentEnvConfig();

  // 如果已经是完整的HTTP URL，进行智能映射
  if (imagePath.startsWith('http')) {
    return mapUrlToCurrentEnvironment(imagePath);
  }

  // 处理相对路径
  if (imagePath.startsWith('/')) {
    return `${config.baseUrl}${imagePath}`;
  }
  return `${config.imageUrl}/${imagePath}`;
}

/**
 * 将URL映射到当前环境
 * @param {string} url 原始URL
 * @returns {string} 映射后的URL
 */
function mapUrlToCurrentEnvironment(url) {
  if (!url || !url.startsWith('http')) return url;

  const config = getCurrentEnvConfig();

  // 定义需要映射的localhost地址列表
  const localhostPatterns = [
    'http://localhost:5000',
    'http://127.0.0.1:5000',
    'https://localhost:5000',
    'https://127.0.0.1:5000'
  ];

  // 检查是否需要映射
  for (const pattern of localhostPatterns) {
    if (url.startsWith(pattern)) {
      // 替换为当前环境的baseUrl
      return url.replace(pattern, config.baseUrl);
    }
  }

  // 如果不需要映射，返回原URL
  return url;
}

/**
 * 获取视频完整URL（智能映射）
 * @param {string} videoPath 视频路径
 * @returns {string} 完整的视频URL
 */
function getVideoUrl(videoPath = '') {
  if (!videoPath) return '';

  const config = getCurrentEnvConfig();

  // 如果已经是完整的HTTP URL，进行智能映射
  if (videoPath.startsWith('http')) {
    return mapUrlToCurrentEnvironment(videoPath);
  }

  // 处理相对路径
  if (videoPath.startsWith('/')) {
    return `${config.baseUrl}${videoPath}`;
  }
  return `${config.videoUrl}/${videoPath}`;
}

/**
 * 日志输出（开发环境才输出）
 * @param {...any} args 日志参数
 */
function log(...args) {
  if (isDevelopment()) {
    console.log('[ENV]', ...args);
  }
}

// 导出配置和工具函数
module.exports = {
  ENV_CONFIG,
  APP_CONFIG,
  getCurrentEnvConfig,
  getFullConfig,
  isDevelopment,
  isProduction,
  getApiUrl,
  getImageUrl,
  getVideoUrl,
  mapUrlToCurrentEnvironment,
  log
};
