#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单打包脚本 - 避免编码问题
"""

import os
import sys
import subprocess
import shutil

def main():
    print("漆器商城后端服务打包工具")
    print("=" * 30)
    
    # 检查文件
    if not os.path.exists('app.py'):
        print("错误: 未找到app.py文件")
        return False
    
    # 安装PyInstaller
    print("正在安装PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("PyInstaller安装成功")
    except:
        print("PyInstaller安装失败")
        return False
    
    # 安装依赖
    print("正在安装依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("依赖安装成功")
    except:
        print("依赖安装失败")
        return False
    
    # 打包
    print("正在打包...")
    cmd = [
        "pyinstaller",
        "--onefile",
        "--console", 
        "--name", "lacquerware-backend",
        "--add-data", "templates;templates",
        "--add-data", "static;static",
        "--add-data", "data;data", 
        "--add-data", "uploads;uploads",
        "app.py"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("打包成功")
    except:
        print("打包失败")
        return False
    
    # 创建启动脚本
    bat_content = '''@echo off
echo Lacquerware Backend Service
echo ==========================
echo Starting service...
echo.
echo Access URL: http://127.0.0.1:5000
echo.
echo Press Ctrl+C to stop
echo.

lacquerware-backend.exe

pause
'''
    
    try:
        with open('dist/start.bat', 'w', encoding='utf-8') as f:
            f.write(bat_content)
        print("启动脚本创建成功")
    except Exception as e:
        print(f"启动脚本创建失败: {e}")
    
    # 创建说明文件
    readme = '''Lacquerware Backend Service
===========================

Files:
- lacquerware-backend.exe  Main program
- start.bat               Startup script

Usage:
Double click start.bat to run the service

Access:
- Admin: http://127.0.0.1:5000
- API: http://127.0.0.1:5000/api/

Notes:
- Ensure port 5000 is available
- Press Ctrl+C to stop service
'''
    
    try:
        with open('dist/README.txt', 'w', encoding='utf-8') as f:
            f.write(readme)
        print("说明文件创建成功")
    except Exception as e:
        print(f"说明文件创建失败: {e}")
    
    # 清理
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            os.remove(file)
    
    print("\n打包完成!")
    print("=" * 30)
    print("输出目录: dist/")
    print("主程序: dist/lacquerware-backend.exe")
    print("启动脚本: dist/start.bat")
    print("=" * 30)
    
    return True

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except Exception as e:
        print(f"错误: {e}")
        input("按回车键退出...")
