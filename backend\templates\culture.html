{% extends "base.html" %}

{% block title %}文化内容管理 - 漆器文化商城管理后台{% endblock %}

{% block page_title %}文化内容管理{% endblock %}

{% block content %}
<!-- 操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <button class="btn btn-primary" onclick="showAddArticleModal()">
            <i class="fas fa-plus me-1"></i>添加文章
        </button>
        <button class="btn btn-outline-secondary" onclick="refreshArticles()">
            <i class="fas fa-sync-alt me-1"></i>刷新
        </button>
    </div>
    <div class="input-group" style="width: 300px;">
        <input type="text" class="form-control" placeholder="搜索文章..." id="searchInput">
        <button class="btn btn-outline-secondary" type="button">
            <i class="fas fa-search"></i>
        </button>
    </div>
</div>

<!-- 文章列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-newspaper me-2"></i>
            文章列表 ({{ articles|length }} 篇文章)
        </h5>
    </div>
    <div class="card-body">
        {% if articles %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="100">封面</th>
                            <th>标题</th>
                            <th width="120">分类</th>
                            <th width="100">作者</th>
                            <th width="120">发布日期</th>
                            <th width="80">浏览量</th>
                            <th width="80">点赞</th>
                            <th width="100">状态</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for article in articles %}
                        <tr>
                            <td>
                                <img src="{{ article.cover_image }}" alt="{{ article.title }}" 
                                     class="img-thumbnail" style="width: 80px; height: 60px; object-fit: cover;">
                            </td>
                            <td>
                                <strong>{{ article.title }}</strong>
                                <br>
                                <small class="text-muted">{{ article.subtitle }}</small>
                                <br>
                                <small class="text-muted">{{ article.summary[:50] }}...</small>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ article.category }}</span>
                            </td>
                            <td>{{ article.author }}</td>
                            <td>{{ article.publish_date }}</td>
                            <td>
                                <i class="fas fa-eye text-muted me-1"></i>
                                {{ article.views }}
                            </td>
                            <td>
                                <i class="fas fa-heart text-danger me-1"></i>
                                {{ article.likes }}
                            </td>
                            <td>
                                <span class="badge {% if article.status == 'published' %}bg-success{% else %}bg-secondary{% endif %}">
                                    {% if article.status == 'published' %}已发布{% else %}草稿{% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewArticle({{ article.id }})" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="editArticle({{ article.id }})" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteArticle({{ article.id }})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">还没有添加任何文章</h5>
                <p class="text-muted">点击上方的添加按钮开始创建文章</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 添加/编辑文章模态框 -->
<div class="modal fade" id="articleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="articleModalTitle">添加文章</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="articleForm">
                    <input type="hidden" id="articleId" name="id">
                    
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">基本信息</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">文章标题 *</label>
                                <input type="text" class="form-control" id="articleTitle" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">副标题</label>
                                <input type="text" class="form-control" id="articleSubtitle" name="subtitle">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">分类 *</label>
                                        <select class="form-select" id="articleCategory" name="category" required>
                                            <option value="">请选择分类</option>
                                            <option value="工艺传承">工艺传承</option>
                                            <option value="艺术创新">艺术创新</option>
                                            <option value="文化历史">文化历史</option>
                                            <option value="大师访谈">大师访谈</option>
                                            <option value="收藏鉴赏">收藏鉴赏</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" id="articleStatus" name="status">
                                            <option value="published">发布</option>
                                            <option value="draft">草稿</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">作者</label>
                                        <input type="text" class="form-control" id="articleAuthor" name="author" value="文化研究院">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">发布日期</label>
                                        <input type="date" class="form-control" id="articleDate" name="publish_date">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">封面图片 *</label>
                                <input type="text" class="form-control" id="articleCoverImage" name="cover_image" placeholder="/images/culture.jpg" required>
                                <small class="form-text text-muted">请先在图片管理中上传图片，然后复制路径</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">文章摘要</label>
                                <textarea class="form-control" id="articleSummary" name="summary" rows="3" placeholder="简要介绍文章内容..."></textarea>
                            </div>
                        </div>
                        
                        <!-- 内容编辑 -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">文章内容</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">正文内容 *</label>
                                <textarea class="form-control" id="articleContent" name="content" rows="15" required placeholder="请输入文章正文内容..."></textarea>
                                <small class="form-text text-muted">支持HTML格式，可以添加图片、链接等</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">标签</label>
                                <input type="text" class="form-control" id="articleTags" name="tags" placeholder="传统工艺,文化传承,历史">
                                <small class="form-text text-muted">多个标签用逗号分隔</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">相关图片</label>
                                <textarea class="form-control" id="articleImages" name="images" rows="3" placeholder="/images/culture1-1.jpg,/images/culture1-2.jpg"></textarea>
                                <small class="form-text text-muted">多个图片路径用逗号分隔</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveArticle()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 文章详情查看模态框 -->
<div class="modal fade" id="articleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">文章详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="articleDetailContent">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="editArticleFromDetail()">编辑文章</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let currentArticleId = null;

    // 显示添加文章模态框
    function showAddArticleModal() {
        document.getElementById('articleModalTitle').textContent = '添加文章';
        document.getElementById('articleForm').reset();
        document.getElementById('articleId').value = '';
        // 设置默认日期为今天
        document.getElementById('articleDate').value = new Date().toISOString().split('T')[0];
        currentArticleId = null;
        new bootstrap.Modal(document.getElementById('articleModal')).show();
    }

    // 编辑文章
    function editArticle(articleId) {
        fetch(`/api/get_article/${articleId}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const article = result.data;
                    fillArticleForm(article);
                    document.getElementById('articleModalTitle').textContent = '编辑文章';
                    currentArticleId = articleId;
                    new bootstrap.Modal(document.getElementById('articleModal')).show();
                } else {
                    showError('获取文章信息失败：' + result.message);
                }
            })
            .catch(error => {
                showError('获取文章信息失败：' + error);
            });
    }

    // 填充文章表单
    function fillArticleForm(article) {
        document.getElementById('articleId').value = article.id;
        document.getElementById('articleTitle').value = article.title;
        document.getElementById('articleSubtitle').value = article.subtitle || '';
        document.getElementById('articleCategory').value = article.category;
        document.getElementById('articleStatus').value = article.status;
        document.getElementById('articleAuthor').value = article.author;
        document.getElementById('articleDate').value = article.publish_date;
        document.getElementById('articleCoverImage').value = article.cover_image;
        document.getElementById('articleSummary').value = article.summary || '';
        document.getElementById('articleContent').value = article.content;
        document.getElementById('articleTags').value = article.tags ? article.tags.join(',') : '';
        document.getElementById('articleImages').value = article.images ? article.images.join(',') : '';
    }

    // 保存文章
    function saveArticle() {
        const form = document.getElementById('articleForm');
        const formData = new FormData(form);
        
        const data = {};
        for (let [key, value] of formData.entries()) {
            if (key === 'tags' || key === 'images') {
                data[key] = value ? value.split(',').map(item => item.trim()).filter(item => item) : [];
            } else {
                data[key] = value;
            }
        }

        const url = currentArticleId ? '/api/update_article' : '/api/add_article';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess(result.message);
                bootstrap.Modal.getInstance(document.getElementById('articleModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showError('保存失败：' + result.message);
            }
        })
        .catch(error => {
            showError('保存失败：' + error);
        });
    }

    // 查看文章详情
    function viewArticle(articleId) {
        fetch(`/api/get_article/${articleId}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const article = result.data;
                    showArticleDetail(article);
                } else {
                    showError('获取文章详情失败：' + result.message);
                }
            })
            .catch(error => {
                showError('获取文章详情失败：' + error);
            });
    }

    // 显示文章详情
    function showArticleDetail(article) {
        const content = `
            <div class="row">
                <div class="col-md-4">
                    <img src="${article.cover_image}" alt="${article.title}" class="img-fluid rounded mb-3">
                    
                    <div class="card">
                        <div class="card-body">
                            <h6>文章信息</h6>
                            <ul class="list-unstyled mb-0">
                                <li><strong>分类：</strong> ${article.category}</li>
                                <li><strong>作者：</strong> ${article.author}</li>
                                <li><strong>发布：</strong> ${article.publish_date}</li>
                                <li><strong>浏览：</strong> ${article.views} 次</li>
                                <li><strong>点赞：</strong> ${article.likes} 次</li>
                                <li><strong>状态：</strong> 
                                    <span class="badge ${article.status === 'published' ? 'bg-success' : 'bg-secondary'}">
                                        ${article.status === 'published' ? '已发布' : '草稿'}
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <h2>${article.title}</h2>
                    ${article.subtitle ? `<h5 class="text-muted mb-3">${article.subtitle}</h5>` : ''}
                    
                    ${article.summary ? `
                    <div class="alert alert-info">
                        <strong>摘要：</strong> ${article.summary}
                    </div>
                    ` : ''}
                    
                    <div class="article-content">
                        ${article.content.replace(/\n/g, '<br>')}
                    </div>
                    
                    ${article.tags && article.tags.length > 0 ? `
                    <div class="mt-4">
                        <strong>标签：</strong>
                        ${article.tags.map(tag => `<span class="badge bg-primary me-1">${tag}</span>`).join('')}
                    </div>
                    ` : ''}
                    
                    ${article.images && article.images.length > 0 ? `
                    <div class="mt-4">
                        <h6>相关图片</h6>
                        <div class="row">
                            ${article.images.map(img => `
                                <div class="col-md-6 mb-2">
                                    <img src="${img}" class="img-fluid rounded" alt="相关图片">
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        document.getElementById('articleDetailContent').innerHTML = content;
        currentArticleId = article.id;
        new bootstrap.Modal(document.getElementById('articleDetailModal')).show();
    }

    // 从详情页编辑文章
    function editArticleFromDetail() {
        bootstrap.Modal.getInstance(document.getElementById('articleDetailModal')).hide();
        setTimeout(() => editArticle(currentArticleId), 300);
    }

    // 删除文章
    function deleteArticle(articleId) {
        if (confirm('确定要删除这篇文章吗？删除后无法恢复！')) {
            fetch('/api/delete_article', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({id: articleId})
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccess('文章删除成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showError('删除失败：' + result.message);
                }
            })
            .catch(error => {
                showError('删除失败：' + error);
            });
        }
    }

    // 刷新文章列表
    function refreshArticles() {
        location.reload();
    }
</script>

<style>
    .article-content {
        line-height: 1.8;
        font-size: 16px;
    }
    
    .article-content img {
        max-width: 100%;
        height: auto;
        margin: 10px 0;
        border-radius: 4px;
    }
</style>
{% endblock %}
