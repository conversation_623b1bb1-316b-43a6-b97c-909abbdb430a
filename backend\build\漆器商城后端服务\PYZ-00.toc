('E:\\Desktop\\xcx\\backend\\build\\漆器商城后端服务\\PYZ-00.pyz',
 [('__future__', 'D:\\applications\\miniconda\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\applications\\miniconda\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize', 'D:\\applications\\miniconda\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\applications\\miniconda\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\applications\\miniconda\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'D:\\applications\\miniconda\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'D:\\applications\\miniconda\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\applications\\miniconda\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\applications\\miniconda\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal', 'D:\\applications\\miniconda\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\applications\\miniconda\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\applications\\miniconda\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\applications\\miniconda\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\applications\\miniconda\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\applications\\miniconda\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\applications\\miniconda\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\applications\\miniconda\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\applications\\miniconda\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\applications\\miniconda\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\applications\\miniconda\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\applications\\miniconda\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\applications\\miniconda\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\applications\\miniconda\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\applications\\miniconda\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\applications\\miniconda\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\applications\\miniconda\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\applications\\miniconda\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\applications\\miniconda\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\applications\\miniconda\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\applications\\miniconda\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\applications\\miniconda\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\applications\\miniconda\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\applications\\miniconda\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\applications\\miniconda\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\applications\\miniconda\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\applications\\miniconda\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\applications\\miniconda\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\applications\\miniconda\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\applications\\miniconda\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\applications\\miniconda\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._saferef',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\_saferef.py',
   'PYMODULE'),
  ('blinker._utilities',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'D:\\applications\\miniconda\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\applications\\miniconda\\Lib\\calendar.py', 'PYMODULE'),
  ('click',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('code', 'D:\\applications\\miniconda\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\applications\\miniconda\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\applications\\miniconda\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\applications\\miniconda\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'D:\\applications\\miniconda\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'D:\\applications\\miniconda\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\applications\\miniconda\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\applications\\miniconda\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\applications\\miniconda\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\applications\\miniconda\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\applications\\miniconda\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\applications\\miniconda\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\applications\\miniconda\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'D:\\applications\\miniconda\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'D:\\applications\\miniconda\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\applications\\miniconda\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\applications\\miniconda\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\applications\\miniconda\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\applications\\miniconda\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\applications\\miniconda\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\applications\\miniconda\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\applications\\miniconda\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\applications\\miniconda\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\applications\\miniconda\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\applications\\miniconda\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\applications\\miniconda\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\applications\\miniconda\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\applications\\miniconda\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\applications\\miniconda\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\applications\\miniconda\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\applications\\miniconda\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\applications\\miniconda\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\applications\\miniconda\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\applications\\miniconda\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\applications\\miniconda\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\applications\\miniconda\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\applications\\miniconda\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\applications\\miniconda\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\applications\\miniconda\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\applications\\miniconda\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('flask',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_cors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.core',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\applications\\miniconda\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\applications\\miniconda\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\applications\\miniconda\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\applications\\miniconda\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\applications\\miniconda\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\applications\\miniconda\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\applications\\miniconda\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\applications\\miniconda\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\applications\\miniconda\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\applications\\miniconda\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\applications\\miniconda\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\applications\\miniconda\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\applications\\miniconda\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\applications\\miniconda\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\applications\\miniconda\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\applications\\miniconda\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\applications\\miniconda\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\applications\\miniconda\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\applications\\miniconda\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\applications\\miniconda\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\applications\\miniconda\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\applications\\miniconda\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\applications\\miniconda\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\applications\\miniconda\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\applications\\miniconda\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\applications\\miniconda\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\applications\\miniconda\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\applications\\miniconda\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\applications\\miniconda\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\applications\\miniconda\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\applications\\miniconda\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\applications\\miniconda\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\applications\\miniconda\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\applications\\miniconda\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\applications\\miniconda\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\applications\\miniconda\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\applications\\miniconda\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\applications\\miniconda\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'D:\\applications\\miniconda\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\applications\\miniconda\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pickle', 'D:\\applications\\miniconda\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\applications\\miniconda\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\applications\\miniconda\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\applications\\miniconda\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\applications\\miniconda\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\applications\\miniconda\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\applications\\miniconda\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\applications\\miniconda\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'D:\\applications\\miniconda\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\applications\\miniconda\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\applications\\miniconda\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\applications\\miniconda\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\applications\\miniconda\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\applications\\miniconda\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\applications\\miniconda\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\applications\\miniconda\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\applications\\miniconda\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\applications\\miniconda\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\applications\\miniconda\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'D:\\applications\\miniconda\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\applications\\miniconda\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\applications\\miniconda\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\applications\\miniconda\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\applications\\miniconda\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\applications\\miniconda\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\applications\\miniconda\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig',
   'D:\\applications\\miniconda\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile', 'D:\\applications\\miniconda\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\applications\\miniconda\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\applications\\miniconda\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\applications\\miniconda\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\applications\\miniconda\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\applications\\miniconda\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'D:\\applications\\miniconda\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\applications\\miniconda\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\applications\\miniconda\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\applications\\miniconda\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\applications\\miniconda\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\applications\\miniconda\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\applications\\miniconda\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\applications\\miniconda\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\applications\\miniconda\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\applications\\miniconda\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\applications\\miniconda\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\applications\\miniconda\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\applications\\miniconda\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\applications\\miniconda\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\applications\\miniconda\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\applications\\miniconda\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\applications\\miniconda\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\applications\\miniconda\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\applications\\miniconda\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\applications\\miniconda\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\applications\\miniconda\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\applications\\miniconda\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\applications\\miniconda\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\applications\\miniconda\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'D:\\applications\\miniconda\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\applications\\miniconda\\Lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml', 'D:\\applications\\miniconda\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\applications\\miniconda\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\applications\\miniconda\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\applications\\miniconda\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\applications\\miniconda\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\applications\\miniconda\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\applications\\miniconda\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\applications\\miniconda\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\applications\\miniconda\\Lib\\zipimport.py', 'PYMODULE')])
