// utils/api.js
// API工具模块 - 封装所有后端API调用

const app = getApp();
const envConfig = require('../config/env.js');
const { urlMapper } = require('./url-mapper.js');

// 获取API配置（从环境配置文件获取）
function getApiConfig() {
  // 优先从app获取配置，如果app未初始化则直接从环境配置获取
  if (app && app.globalData && app.globalData.config) {
    return app.globalData.config;
  }

  // 如果app还未初始化，直接从环境配置获取
  return envConfig.getFullConfig();
}

// 数据缓存
const dataCache = {
  home: { data: null, timestamp: 0 },
  category: { data: null, timestamp: 0 },
  user: { data: null, timestamp: 0 },
  config: { data: null, timestamp: 0 }
};

/**
 * 发起HTTP请求的基础方法
 * @param {Object} options 请求配置
 * @returns {Promise} 请求结果
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const apiConfig = getApiConfig();
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      timeout = apiConfig.timeout,
      showLoading = true,
      loadingText = '加载中...'
    } = options;

    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }

    // 构建完整URL
    let fullUrl;
    if (url.startsWith('http')) {
      fullUrl = url;
    } else if (url.startsWith('/api/')) {
      // API接口使用apiUrl
      fullUrl = `${apiConfig.apiUrl}${url.substring(4)}`;
    } else if (url.startsWith('/')) {
      // 其他路径使用baseUrl
      fullUrl = `${apiConfig.baseUrl}${url}`;
    } else {
      // 相对路径默认使用apiUrl
      fullUrl = `${apiConfig.apiUrl}/${url}`;
    }

    // 设置默认请求头
    const defaultHeader = {
      'Content-Type': 'application/json',
      ...header
    };

    wx.request({
      url: fullUrl,
      method: method.toUpperCase(),
      data: data,
      header: defaultHeader,
      timeout: timeout,
      success: (res) => {
        if (showLoading) {
          wx.hideLoading();
        }

        // 处理HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          console.error('API请求失败:', res);
          reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`));
        }
      },
      fail: (err) => {
        if (showLoading) {
          wx.hideLoading();
        }
        
        console.error('网络请求失败:', err);
        
        // 处理网络错误
        let errorMessage = '网络连接失败';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接';
          } else if (err.errMsg.includes('fail')) {
            errorMessage = '网络连接失败，请检查网络设置';
          }
        }
        
        reject(new Error(errorMessage));
      }
    });
  });
}

/**
 * GET请求
 * @param {string} url 请求地址
 * @param {Object} params 查询参数
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function get(url, params = {}, options = {}) {
  // 将参数拼接到URL中
  if (Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    url += (url.includes('?') ? '&' : '?') + queryString;
  }

  return request({
    url,
    method: 'GET',
    ...options
  });
}

/**
 * POST请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 * @param {string} url 请求地址
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function del(url, options = {}) {
  return request({
    url,
    method: 'DELETE',
    ...options
  });
}

// ==================== 具体业务API ====================

/**
 * 获取小程序数据
 * @returns {Promise} 小程序数据
 */
function getMiniProgramData() {
  return get('/api/export_data', {}, {
    showLoading: true,
    loadingText: '加载数据中...'
  });
}

/**
 * 获取首页数据
 * @param {boolean} useCache 是否使用缓存
 * @returns {Promise} 首页数据
 */
function getHomeData(useCache = true) {
  // 检查缓存
  if (useCache && isCacheValid('home')) {
    return Promise.resolve(getCache('home'));
  }

  return get('/api/miniprogram/home').then(response => {
    if (response.success && response.data) {
      let homeData = {
        guideData: response.data.guide_data || {},
        quickActions: response.data.quick_actions || [],
        videoList: response.data.video_list || [],
        timelineData: response.data.timeline_data || [],
        recommendProducts: response.data.recommend_products || []
      };

      // 使用URL映射器处理数据中的URL
      homeData = urlMapper.mapHomeData(homeData);

      // 设置缓存
      if (useCache) {
        setCache('home', homeData);
      }

      return homeData;
    } else {
      throw new Error(response.message || '获取首页数据失败');
    }
  });
}

/**
 * 获取分类数据
 * @param {boolean} useCache 是否使用缓存
 * @returns {Promise} 分类数据
 */
function getCategoryData(useCache = true) {
  // 检查缓存
  if (useCache && isCacheValid('category')) {
    return Promise.resolve(getCache('category'));
  }

  return get('/api/miniprogram/category').then(response => {
    if (response.success && response.data) {
      const categoryData = {
        categories: response.data.categories || []
      };

      // 设置缓存
      if (useCache) {
        setCache('category', categoryData);
      }

      return categoryData;
    } else {
      throw new Error(response.message || '获取分类数据失败');
    }
  });
}

/**
 * 获取用户数据
 * @param {boolean} useCache 是否使用缓存
 * @returns {Promise} 用户数据
 */
function getUserData(useCache = true) {
  // 检查缓存
  if (useCache && isCacheValid('user')) {
    return Promise.resolve(getCache('user'));
  }

  return get('/api/miniprogram/user').then(response => {
    if (response.success && response.data) {
      let userData = {
        userInfo: response.data.default_user || {},
        assets: response.data.assets || {}
      };

      // 使用URL映射器处理用户数据中的URL
      userData = urlMapper.mapUserData(userData);

      // 设置缓存
      if (useCache) {
        setCache('user', userData);
      }

      return userData;
    } else {
      throw new Error(response.message || '获取用户数据失败');
    }
  });
}

/**
 * 获取应用配置
 * @param {boolean} useCache 是否使用缓存
 * @returns {Promise} 应用配置
 */
function getAppConfig(useCache = true) {
  // 检查缓存
  if (useCache && isCacheValid('config')) {
    return Promise.resolve(getCache('config'));
  }

  return get('/api/miniprogram/config').then(response => {
    if (response.success && response.data) {
      const configData = response.data;

      // 设置缓存
      if (useCache) {
        setCache('config', configData);
      }

      return configData;
    } else {
      throw new Error(response.message || '获取应用配置失败');
    }
  });
}

/**
 * 获取文化数据
 * @param {boolean} useCache 是否使用缓存
 * @returns {Promise} 文化数据
 */
function getCultureData(useCache = true) {
  // 检查缓存
  if (useCache && isCacheValid('culture')) {
    return Promise.resolve(getCache('culture'));
  }

  return get('/api/miniprogram/culture').then(response => {
    if (response.success && response.data) {
      let cultureData = response.data;

      // 使用URL映射器处理文化数据中的URL
      if (cultureData.articles && Array.isArray(cultureData.articles)) {
        cultureData.articles = cultureData.articles.map(article => ({
          ...article,
          cover_image: urlMapper.mapImageUrl(article.cover_image),
          images: article.images ? article.images.map(img => urlMapper.mapImageUrl(img)) : []
        }));
      }

      // 设置缓存
      if (useCache) {
        setCache('culture', cultureData);
      }

      return cultureData;
    } else {
      throw new Error(response.message || '获取文化数据失败');
    }
  });
}

/**
 * 上传文件
 * @param {string} filePath 文件路径
 * @param {Object} options 上传配置
 * @returns {Promise} 上传结果
 */
function uploadFile(filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const apiConfig = getApiConfig();
    const {
      showLoading = true,
      loadingText = '上传中...'
    } = options;

    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true
      });
    }

    wx.uploadFile({
      url: apiConfig.uploadUrl,
      filePath: filePath,
      name: 'file',
      success: (res) => {
        if (showLoading) {
          wx.hideLoading();
        }

        try {
          const data = JSON.parse(res.data);
          if (data.success) {
            resolve(data);
          } else {
            reject(new Error(data.message || '上传失败'));
          }
        } catch (e) {
          reject(new Error('上传响应解析失败'));
        }
      },
      fail: (err) => {
        if (showLoading) {
          wx.hideLoading();
        }
        reject(new Error('上传失败'));
      }
    });
  });
}

/**
 * 错误处理工具函数
 * @param {Error} error 错误对象
 * @param {string} defaultMessage 默认错误消息
 */
function handleError(error, defaultMessage = '操作失败') {
  console.error('API错误:', error);
  
  let message = defaultMessage;
  if (error && error.message) {
    message = error.message;
  }

  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
}

/**
 * 检查网络状态
 * @returns {Promise} 网络状态
 */
function checkNetworkStatus() {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        resolve({
          isConnected: res.networkType !== 'none',
          networkType: res.networkType
        });
      },
      fail: () => {
        resolve({
          isConnected: false,
          networkType: 'unknown'
        });
      }
    });
  });
}

/**
 * 检查缓存是否有效
 * @param {string} cacheKey 缓存键
 * @returns {boolean} 缓存是否有效
 */
function isCacheValid(cacheKey) {
  const cache = dataCache[cacheKey];
  if (!cache || !cache.data) {
    return false;
  }

  const apiConfig = getApiConfig();
  const now = Date.now();
  return (now - cache.timestamp) < apiConfig.cacheTimeout;
}

/**
 * 设置缓存
 * @param {string} cacheKey 缓存键
 * @param {any} data 缓存数据
 */
function setCache(cacheKey, data) {
  dataCache[cacheKey] = {
    data: data,
    timestamp: Date.now()
  };
}

/**
 * 获取缓存
 * @param {string} cacheKey 缓存键
 * @returns {any} 缓存数据
 */
function getCache(cacheKey) {
  const cache = dataCache[cacheKey];
  return cache ? cache.data : null;
}

/**
 * 清除指定缓存
 * @param {string} cacheKey 缓存键
 */
function clearCache(cacheKey) {
  if (dataCache[cacheKey]) {
    dataCache[cacheKey] = { data: null, timestamp: 0 };
  }
}

/**
 * 清除所有缓存
 */
function clearAllCache() {
  Object.keys(dataCache).forEach(key => {
    dataCache[key] = { data: null, timestamp: 0 };
  });
}

// 导出API方法
module.exports = {
  // 基础请求方法
  request,
  get,
  post,
  put,
  del,

  // 业务API
  getMiniProgramData,
  getHomeData,
  getCategoryData,
  getUserData,
  getAppConfig,
  getCultureData,
  uploadFile,

  // 工具方法
  handleError,
  checkNetworkStatus,

  // 缓存方法
  isCacheValid,
  setCache,
  getCache,
  clearCache,
  clearAllCache,

  // 配置方法
  getApiConfig
};
