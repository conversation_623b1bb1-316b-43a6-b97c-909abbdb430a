# 漆器文化商城 - 后台管理系统

## 🎯 功能特色

这是一个基于Flask的后台管理系统，专门为漆器文化商城小程序设计，提供以下功能：

### 📊 仪表盘
- 数据统计概览
- 快速操作入口
- 内容概览展示
- 项目历程管理

### 📝 内容管理
- **首页内容管理**：指南卡片、快捷入口、视频轮播、项目历程
- **商品分类管理**：分类名称、数量、标识管理
- **用户信息管理**：默认用户信息、资产设置

### 🖼️ 图片管理
- 图片上传（支持拖拽）
- 图片预览和删除
- 网格/列表视图切换
- 图片路径复制

### ⚙️ 系统设置
- 小程序配置（名称、主题色）
- 数据导入/导出
- 实时预览功能
- 数据重置功能

## 🚀 快速开始

### 1. 环境要求
- Python 3.8+
- Flask 2.3+

### 2. 安装依赖
```bash
cd admin
pip install -r requirements.txt
```

### 3. 启动服务
```bash
python app.py
```

### 4. 访问管理后台
打开浏览器访问：http://localhost:5000

## 📁 项目结构

```
admin/
├── app.py                 # Flask应用主文件
├── requirements.txt       # Python依赖
├── README.md             # 使用说明
├── data/                 # 数据存储目录
│   └── miniprogram_data.json  # 小程序配置数据
├── uploads/              # 上传文件目录
├── templates/            # HTML模板
│   ├── base.html        # 基础模板
│   ├── index.html       # 仪表盘
│   ├── content.html     # 内容管理
│   ├── images.html      # 图片管理
│   └── settings.html    # 系统设置
└── static/              # 静态资源
    ├── css/
    └── js/
```

## 🔧 配置说明

### 数据文件
系统会自动创建 `data/miniprogram_data.json` 文件来存储所有配置数据，包括：
- 应用配置（名称、颜色主题）
- 首页内容（指南、快捷入口、视频、历程）
- 商品分类信息
- 用户默认信息

### 图片管理
- 图片存储在 `../images/` 目录（小程序的images目录）
- 支持的格式：PNG, JPG, JPEG, GIF, WEBP
- 最大文件大小：16MB

## 📋 使用指南

### 1. 内容管理
1. 点击左侧导航的"内容管理"
2. 使用标签页切换不同内容类型
3. 修改内容后点击"保存"按钮
4. 系统会自动同步到小程序

### 2. 图片管理
1. 点击左侧导航的"图片管理"
2. 拖拽图片到上传区域或点击选择文件
3. 上传成功后可以复制图片路径
4. 在内容管理中使用图片路径

### 3. 系统设置
1. 点击左侧导航的"系统设置"
2. 修改应用名称和主题色
3. 使用实时预览查看效果
4. 导出/导入配置数据

## 🔄 数据同步

管理后台的所有修改都会自动保存到 `data/miniprogram_data.json` 文件中。要将这些配置应用到小程序：

1. 在管理后台修改内容
2. 点击"同步数据"按钮
3. 重新编译小程序即可看到更新

## 🛠️ 开发说明

### API接口
- `POST /api/save_data` - 保存数据
- `POST /api/upload` - 上传图片
- `POST /api/delete_image` - 删除图片
- `GET /api/export_data` - 导出数据
- `POST /api/import_data` - 导入数据
- `POST /api/reset_data` - 重置数据

### 扩展功能
如需添加新功能，可以：
1. 在 `app.py` 中添加新的路由
2. 在 `templates/` 中创建新的模板
3. 更新数据结构和API接口

## 🔒 安全注意事项

1. **生产环境部署**：
   - 修改 `app.secret_key`
   - 设置 `debug=False`
   - 使用HTTPS
   - 添加身份验证

2. **文件上传安全**：
   - 已限制文件类型和大小
   - 使用安全的文件名处理

3. **数据备份**：
   - 定期备份 `data/` 目录
   - 导出配置数据作为备份

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 依赖是否正确安装
3. 端口5000是否被占用
4. 文件权限是否正确

---

**漆器文化商城后台管理系统 v1.0.0**  
*让小程序内容管理变得简单高效*
