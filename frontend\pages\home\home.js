// pages/home/<USER>
const app = getApp();
const api = require('../../utils/api.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    primaryColor: '#FF8C42',
    showCustomNavbar: false,

    // 指南卡片数据
    guideData: {},

    // 快捷入口数据
    quickActions: [],

    // 视频列表数据
    videoList: [],

    // 时间线数据
    timelineData: [],

    // 推荐商品数据
    recommendProducts: [],

    // 文化文章数据
    cultureArticles: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('首页加载', options);
    this.initPageData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('首页渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('首页显示');
    // 更新购物车数量
    this.updateCartCount();

    // 每次显示页面时刷新数据
    this.refreshPageDataSilently();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('首页隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('首页卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshPageData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底');
    this.loadMoreProducts();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '非物质文化遗产·漆器',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    this.setData({
      loading: true
    });

    // 从后端API加载数据
    this.loadHomeData();
  },

  /**
   * 从后端加载首页数据
   */
  async loadHomeData() {
    try {
      // 检查网络状态
      const networkStatus = await api.checkNetworkStatus();
      if (!networkStatus.isConnected) {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }

      // 同时调用首页数据和文化数据API
      const [homeData, cultureData] = await Promise.all([
        api.getHomeData(),
        api.getCultureData()
      ]);

      // 处理视频数据，确保包含showPlayer属性
      const videoList = (homeData.videoList || []).map(video => ({
        ...video,
        showPlayer: false
      }));

      // 处理文化数据
      let cultureArticles = [];
      console.log('文化数据:', cultureData);

      if (cultureData && cultureData.articles) {
        cultureArticles = cultureData.articles.filter(article => article.status === 'published');
        console.log('过滤后的文化文章:', cultureArticles);
      } else {
        console.log('文化数据为空，使用默认数据');
        // 如果文化数据为空，使用默认数据
        cultureArticles = [
          {
            id: 1,
            title: '千年传承 匠心工艺',
            subtitle: '探索中国漆器文化的深厚底蕴',
            cover_image: app.getImageUrl('culture-bg.jpg'),
            author: '文化研究院',
            publish_date: '2024-01-15',
            status: 'published'
          }
        ];
      }

      this.setData({
        guideData: homeData.guideData || [],
        quickActions: homeData.quickActions || [],
        videoList: videoList,
        timelineData: homeData.timelineData || [],
        recommendProducts: homeData.recommendProducts || [],
        cultureArticles: cultureArticles,
        loading: false
      });

      console.log('首页数据加载成功:', homeData);
      console.log('文化数据加载成功:', cultureArticles);
      console.log('快捷入口数量:', homeData.quickActions ? homeData.quickActions.length : 0);
    } catch (error) {
      console.error('加载首页数据失败:', error);

      // 显示错误提示
      api.handleError(error, '加载首页数据失败');

      // 加载失败时使用默认数据
      this.loadDefaultData();

      this.setData({ loading: false });
    }
  },

  /**
   * 加载默认数据（当API调用失败时使用）
   */
  loadDefaultData() {
    this.setData({
      guideData: {
        title: '漆器收藏与鉴赏指南',
        expert_name: '张文华 专家',
        publish_date: '2024年1月15日',
        cover_image: '/images/guide-cover.jpg'
      },
      quickActions: [
        { id: 1, icon: '🎨', text: '工艺介绍', action: 'craft' },
        { id: 2, icon: '📦', text: '商品分类', action: 'category' },
        { id: 3, icon: '✨', text: '定制服务', action: 'custom' }
      ],
      videoList: [
        {
          id: 1,
          title: '传统漆器制作工艺',
          thumbnail: '/images/video1.jpg',
          duration: '05:32'
        }
      ],
      timelineData: [
        { id: 1, year: '2024年', description: '平台正式上线，开始传承漆器文化' }
      ],
      recommendProducts: [
        {
          id: 1,
          name: '传统漆扇 · 牡丹花开',
          price: '288.00',
          image: '/images/product1.jpg',
          category: 'fan'
        }
      ],
      cultureArticles: [
        {
          id: 1,
          title: '千年传承 匠心工艺',
          subtitle: '探索中国漆器文化的深厚底蕴',
          cover_image: app.getImageUrl('culture-bg.jpg'),
          author: '文化研究院',
          publish_date: '2024-01-15',
          status: 'published'
        }
      ]
    });
  },

  /**
   * 刷新页面数据
   */
  async refreshPageData() {
    this.setData({
      loading: true
    });

    try {
      // 先刷新视频配置
      try {
        await api.post('/api/refresh_videos');
        console.log('视频配置已刷新');
      } catch (videoError) {
        console.warn('刷新视频配置失败:', videoError);
        // 不阻断主流程
      }

      // 重新加载数据
      await this.loadHomeData();

      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      wx.stopPullDownRefresh();
      api.handleError(error, '刷新失败');
      this.setData({ loading: false });
    }
  },

  /**
   * 静默刷新页面数据（不显示loading和提示）
   */
  async refreshPageDataSilently() {
    try {
      // 检查是否需要强制刷新
      const shouldRefresh = app.shouldRefreshData('home');

      // 检查网络状态
      const networkStatus = await api.checkNetworkStatus();
      if (!networkStatus.isConnected) {
        return; // 网络不可用时不刷新
      }

      // 如果需要强制刷新，则不使用缓存
      const useCache = !shouldRefresh;

      // 调用API获取最新数据
      const homeData = await api.getHomeData(useCache);

      // 静默更新数据
      this.setData({
        guideData: homeData.guideData || this.data.guideData,
        quickActions: homeData.quickActions || this.data.quickActions,
        videoList: homeData.videoList || this.data.videoList,
        timelineData: homeData.timelineData || this.data.timelineData,
        recommendProducts: homeData.recommendProducts || this.data.recommendProducts
      });

      // 清除刷新标记
      if (shouldRefresh) {
        app.clearRefreshFlag('home');
      }

      console.log('首页数据静默刷新成功', shouldRefresh ? '(强制刷新)' : '(使用缓存)');
    } catch (error) {
      // 静默刷新失败时不显示错误提示，只在控制台记录
      console.warn('首页数据静默刷新失败:', error);
    }
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts() {
    // 模拟加载更多商品
    wx.showLoading({
      title: '加载中...'
    });

    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '已加载全部商品',
        icon: 'none'
      });
    }, 1000);
  },

  /**
   * 更新购物车数量
   */
  updateCartCount() {
    const cartCount = app.globalData.totalCartCount;
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartCount.toString()
      });
    } else {
      wx.removeTabBarBadge({
        index: 2
      });
    }
  },

  /**
   * 指南卡片点击事件
   */
  onGuideCardTap(e) {
    const guide = e.currentTarget.dataset.guide;
    console.log('点击指南卡片:', guide);

    wx.showToast({
      title: `查看${guide.title}`,
      icon: 'none'
    });

    // 这里可以跳转到详情页面
    // wx.navigateTo({
    //   url: `/pages/guide-detail/guide-detail?id=${guide.id}`
    // });
  },

  /**
   * 快捷入口点击事件
   */
  onQuickActionTap(e) {
    const action = e.currentTarget.dataset.action;
    switch (action) {
      case 'craft':
        wx.showToast({
          title: '跳转到工艺介绍',
          icon: 'none'
        });
        break;
      case 'category':
        wx.switchTab({
          url: '/pages/category/category'
        });
        break;
      case 'custom':
        wx.showToast({
          title: '跳转到定制服务',
          icon: 'none'
        });
        break;
    }
  },

  /**
   * 视频点击事件 - 显示视频播放器
   */
  onVideoTap(e) {
    const video = e.currentTarget.dataset.video;

    // 先暂停所有其他视频
    this.pauseAllVideos();

    // 更新视频列表，显示当前视频的播放器
    const videoList = this.data.videoList.map(item => ({
      ...item,
      showPlayer: item.id === video.id
    }));

    this.setData({
      videoList: videoList
    });

    console.log('显示视频播放器:', video.title);
  },

  /**
   * 视频关闭事件 - 隐藏视频播放器
   */
  onVideoClose(e) {
    const video = e.currentTarget.dataset.video;

    // 暂停视频
    const videoContext = wx.createVideoContext(`video-${video.id}`, this);
    videoContext.pause();

    // 隐藏播放器
    const videoList = this.data.videoList.map(item => ({
      ...item,
      showPlayer: false
    }));

    this.setData({
      videoList: videoList
    });

    console.log('隐藏视频播放器:', video.title);
  },

  /**
   * 视频开始播放事件
   */
  onVideoPlay(e) {
    const video = e.currentTarget.dataset.video;
    console.log('视频开始播放:', video.title);

    // 暂停其他所有视频
    this.pauseAllVideos(video.id);
  },

  /**
   * 视频暂停事件
   */
  onVideoPause(e) {
    const video = e.currentTarget.dataset.video;
    console.log('视频暂停:', video.title);
  },

  /**
   * 视频播放结束事件
   */
  onVideoEnded(e) {
    const video = e.currentTarget.dataset.video;
    console.log('视频播放结束:', video.title);
  },

  /**
   * 视频播放错误事件
   */
  onVideoError(e) {
    const video = e.currentTarget.dataset.video;
    const errorDetail = e.detail;
    console.error('视频播放错误:', video.title, errorDetail);

    let errorMessage = '视频播放失败';

    // 根据错误类型提供更具体的提示
    if (errorDetail && errorDetail.errMsg) {
      if (errorDetail.errMsg.includes('SRC_NOT_SUPPORTED')) {
        errorMessage = '视频格式不支持或文件不存在';
      } else if (errorDetail.errMsg.includes('NETWORK')) {
        errorMessage = '网络连接失败，请检查网络';
      } else if (errorDetail.errMsg.includes('DECODE')) {
        errorMessage = '视频解码失败';
      }
    }

    wx.showModal({
      title: '播放失败',
      content: `${errorMessage}\n\n视频：${video.title}`,
      showCancel: false,
      confirmText: '知道了'
    });

    // 自动隐藏播放器
    const videoList = this.data.videoList.map(item => ({
      ...item,
      showPlayer: false
    }));
    this.setData({ videoList });
  },

  /**
   * 暂停所有视频
   */
  pauseAllVideos(excludeId = null) {
    this.data.videoList.forEach(video => {
      if (excludeId && video.id === excludeId) {
        return; // 跳过指定的视频
      }

      if (video.showPlayer) {
        const videoContext = wx.createVideoContext(`video-${video.id}`, this);
        videoContext.pause();
      }
    });
  },

  /**
   * 查看更多商品
   */
  onViewMoreProducts() {
    wx.switchTab({
      url: '/pages/category/category'
    });
  },

  /**
   * 商品点击事件
   */
  onProductTap(e) {
    const product = e.currentTarget.dataset.product;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`
    });
  },

  /**
   * 添加到购物车
   */
  onAddToCart(e) {
    const product = e.currentTarget.dataset.product;
    app.addToCart(product);
    this.updateCartCount();
  },

  /**
   * 文化介绍点击事件
   */
  onCultureTap(e) {
    const article = e.currentTarget.dataset.article;
    if (article) {
      // 跳转到文章详情页面（如果有的话）
      wx.showModal({
        title: article.title,
        content: article.subtitle || article.summary || '探索中国漆器文化的深厚底蕴',
        showCancel: false,
        confirmText: '了解更多',
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '文章详情页开发中',
              icon: 'none'
            });
          }
        }
      });
    } else {
      wx.showToast({
        title: '跳转到文化介绍',
        icon: 'none'
      });
    }
  }
});