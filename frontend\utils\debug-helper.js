// utils/debug-helper.js
// 调试辅助工具 - 在小程序中使用

const { urlMapper } = require('./url-mapper.js');
const envConfig = require('../config/env.js');

/**
 * 调试辅助类
 */
class DebugHelper {
  constructor() {
    this.app = getApp();
  }

  /**
   * 显示当前环境配置信息
   */
  showEnvironmentInfo() {
    const config = this.app.getCurrentConfig();
    const mapperInfo = urlMapper.getMapperInfo();
    
    console.log('=== 环境配置信息 ===');
    console.log('当前环境:', envConfig.ENV_CONFIG.current);
    console.log('服务器配置:', config);
    console.log('URL映射信息:', mapperInfo);
    console.log('==================');
    
    return {
      environment: envConfig.ENV_CONFIG.current,
      config: config,
      mapperInfo: mapperInfo
    };
  }

  /**
   * 测试URL映射功能
   * @param {Array} testUrls 测试URL列表
   */
  testUrlMapping(testUrls = []) {
    const defaultTestUrls = [
      'http://127.0.0.1:5000/images/test.jpg',
      'http://localhost:5000/images/test.jpg',
      '/images/relative.jpg',
      'filename.jpg'
    ];
    
    const urlsToTest = testUrls.length > 0 ? testUrls : defaultTestUrls;
    
    console.log('=== URL映射测试 ===');
    const results = urlsToTest.map(url => {
      const mappedUrl = urlMapper.mapUrl(url);
      const imageUrl = urlMapper.mapImageUrl(url);
      
      console.log(`原始URL: ${url}`);
      console.log(`映射URL: ${mappedUrl}`);
      console.log(`图片URL: ${imageUrl}`);
      console.log('---');
      
      return {
        original: url,
        mapped: mappedUrl,
        image: imageUrl
      };
    });
    console.log('================');
    
    return results;
  }

  /**
   * 检查页面数据中的URL
   * @param {Object} pageData 页面数据
   * @param {string} pageName 页面名称
   */
  checkPageUrls(pageData, pageName = '未知页面') {
    console.log(`=== ${pageName} URL检查 ===`);
    
    const urlFields = ['url', 'image', 'cover_image', 'thumbnail', 'avatar', 'avatarUrl'];
    const foundUrls = [];
    
    const findUrls = (obj, path = '') => {
      if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
          findUrls(item, `${path}[${index}]`);
        });
      } else if (obj && typeof obj === 'object') {
        Object.keys(obj).forEach(key => {
          const currentPath = path ? `${path}.${key}` : key;
          
          if (urlFields.includes(key) && typeof obj[key] === 'string') {
            foundUrls.push({
              path: currentPath,
              field: key,
              url: obj[key],
              isLocalhost: obj[key].includes('localhost') || obj[key].includes('127.0.0.1'),
              mappedUrl: urlMapper.mapUrl(obj[key])
            });
          } else {
            findUrls(obj[key], currentPath);
          }
        });
      }
    };
    
    findUrls(pageData);
    
    console.log(`找到 ${foundUrls.length} 个URL字段:`);
    foundUrls.forEach(item => {
      console.log(`- ${item.path}: ${item.url}`);
      if (item.isLocalhost) {
        console.log(`  ⚠️ 检测到localhost地址，映射为: ${item.mappedUrl}`);
      }
    });
    console.log('===================');
    
    return foundUrls;
  }

  /**
   * 清除所有缓存并重新加载数据
   */
  clearCacheAndReload() {
    console.log('=== 清除缓存并重新加载 ===');
    
    try {
      const api = require('./api.js');
      if (api.clearAllCache) {
        api.clearAllCache();
        console.log('✅ API缓存已清除');
      }
      
      // 重新加载当前页面
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        if (currentPage.onLoad) {
          console.log('🔄 重新加载当前页面');
          currentPage.onLoad(currentPage.options || {});
        }
      }
      
      console.log('✅ 重新加载完成');
    } catch (error) {
      console.error('❌ 重新加载失败:', error);
    }
    
    console.log('========================');
  }

  /**
   * 验证网络连接
   */
  async checkNetworkConnection() {
    console.log('=== 网络连接检查 ===');
    
    const config = this.app.getCurrentConfig();
    const testUrl = `${config.apiUrl}/health`; // 假设有健康检查接口
    
    try {
      const networkType = await new Promise((resolve) => {
        wx.getNetworkType({
          success: (res) => resolve(res.networkType),
          fail: () => resolve('unknown')
        });
      });
      
      console.log('网络类型:', networkType);
      console.log('测试URL:', testUrl);
      
      // 简单的连接测试
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: testUrl,
          timeout: 5000,
          success: resolve,
          fail: reject
        });
      });
      
      console.log('✅ 网络连接正常');
      console.log('响应状态:', response.statusCode);
      
    } catch (error) {
      console.log('❌ 网络连接异常:', error.errMsg || error.message);
    }
    
    console.log('=================');
  }

  /**
   * 生成调试报告
   */
  generateDebugReport() {
    const report = {
      timestamp: new Date().toISOString(),
      environment: this.showEnvironmentInfo(),
      urlMappingTest: this.testUrlMapping(),
      pages: []
    };
    
    // 获取当前页面信息
    const pages = getCurrentPages();
    pages.forEach((page, index) => {
      const pageInfo = {
        index: index,
        route: page.route,
        data: page.data
      };
      
      // 检查页面数据中的URL
      const urlCheck = this.checkPageUrls(page.data, page.route);
      pageInfo.urlCheck = urlCheck;
      
      report.pages.push(pageInfo);
    });
    
    console.log('=== 调试报告 ===');
    console.log(JSON.stringify(report, null, 2));
    console.log('==============');
    
    return report;
  }
}

// 创建全局实例
const debugHelper = new DebugHelper();

// 在全局对象上添加调试方法，方便在控制台中使用
if (typeof global !== 'undefined') {
  global.debugHelper = debugHelper;
}

// 导出
module.exports = {
  debugHelper,
  DebugHelper
};
