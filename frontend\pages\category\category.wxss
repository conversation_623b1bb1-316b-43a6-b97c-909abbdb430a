/* pages/category/category.wxss */

/* 分类页面容器 */
.category-container {
  padding: var(--spacing-md);
  background-color: var(--cream-white);
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-dark);
}

/* 分类列表 */
.category-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.category-card {
  padding: var(--spacing-lg) var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.category-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 12rpx var(--shadow-light);
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.category-count {
  font-size: 28rpx;
  color: var(--text-gray);
}

.category-arrow {
  font-size: 36rpx;
  color: var(--primary-orange);
  font-weight: bold;
}