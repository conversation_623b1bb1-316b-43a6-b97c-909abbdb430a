# 非物质文化遗产漆器商城小程序软件著作权申请文档

## 一、软件基本信息

### 1.1 软件名称

**非物质文化遗产漆器商城小程序系统**

### 1.2 软件版本号

V1.0

### 1.3 开发完成日期

2024年1月

### 1.4 软件类型

应用软件

### 1.5 运行环境

- 前端：微信小程序平台
- 后端：Python 3.x + Flask框架
- 数据库：JSON文件存储
- 操作系统：跨平台（Windows/Linux/macOS）

### 1.6 编程语言

- JavaScript（微信小程序前端）
- Python（后端服务）
- WXML（微信小程序页面结构）
- WXSS（微信小程序样式）
- HTML（后台管理界面）
- CSS（样式表）

## 二、软件功能概述

### 2.1 软件简介

本软件是一个专注于非物质文化遗产漆器展示、销售和文化传播的综合性小程序平台。系统采用前后端分离架构，前端为微信小程序，后端为Flask Web管理系统，旨在通过数字化手段传承和推广传统漆器文化。

### 2.2 主要功能模块

#### 2.2.1 前端小程序功能

1. **首页展示模块**

   - 漆器文化指南展示
   - 快捷功能入口
   - 视频播放功能
   - 发展时间线
   - 推荐商品展示
2. **商品分类模块**

   - 漆扇、手镯盒、笔筒、茶具等分类
   - 商品数量统计
   - 分类浏览功能
3. **购物车模块**

   - 商品添加/删除
   - 数量调整
   - 价格计算
   - 结算功能
4. **用户中心模块**

   - 用户信息展示
   - 资产管理（优惠券、积分、余额）
   - 个人设置
5. **商品详情模块**

   - 商品图片轮播
   - 详细信息展示
   - 购买功能

#### 2.2.2 后端管理系统功能

1. **内容管理**

   - 首页内容编辑
   - 商品信息管理
   - 文化内容管理
2. **媒体管理**

   - 图片上传管理
   - 视频上传管理
   - 文件服务
3. **系统设置**

   - 主题色配置
   - 应用配置管理
   - 数据同步
4. **数据管理**

   - 数据备份
   - 数据重置
   - API接口服务

## 三、技术架构

### 3.1 系统架构图

```
[微信小程序前端] ←→ [Flask后端API] ←→ [JSON数据存储]
        ↓                    ↓
   [用户交互界面]      [后台管理系统]
```

**【此处插入系统架构图】**

### 3.2 技术栈详情

#### 3.2.1 前端技术

- **微信小程序框架**：原生小程序开发
- **页面结构**：WXML模板语言
- **样式设计**：WXSS样式语言
- **逻辑处理**：JavaScript ES6+
- **状态管理**：小程序原生数据绑定
- **网络请求**：wx.request API

#### 3.2.2 后端技术

- **Web框架**：Flask 2.3.3
- **跨域处理**：Flask-CORS 4.0.0
- **模板引擎**：Jinja2 3.1.2
- **文件处理**：Werkzeug 2.3.7
- **数据存储**：JSON文件系统
- **静态文件服务**：Flask静态文件服务

### 3.3 项目结构

```
xcx/
├── frontend/                 # 微信小程序前端
│   ├── pages/               # 页面文件
│   │   ├── home/           # 首页
│   │   ├── category/       # 分类页
│   │   ├── cart/           # 购物车
│   │   ├── profile/        # 用户中心
│   │   └── product-detail/ # 商品详情
│   ├── components/         # 组件
│   ├── utils/              # 工具函数
│   ├── images/             # 图片资源
│   ├── app.js              # 应用入口
│   ├── app.json            # 应用配置
│   └── app.wxss            # 全局样式
├── backend/                 # Flask后端
│   ├── templates/          # HTML模板
│   ├── static/             # 静态资源
│   ├── uploads/            # 上传文件
│   ├── app.py              # 主应用文件
│   └── requirements.txt    # 依赖包
└── data/                   # 数据文件
    └── miniprogram_data.json
```

## 四、核心代码展示

### 4.1 应用配置文件（app.json）

```json
{
  "pages": [
    "pages/home/<USER>",
    "pages/category/category", 
    "pages/cart/cart",
    "pages/profile/profile",
    "pages/product-detail/product-detail"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#FF8C42",
    "navigationBarTitleText": "非物质文化遗产·漆器",
    "navigationBarTextStyle": "white",
    "backgroundColor": "#FFF8F0",
    "enablePullDownRefresh": true,
    "onReachBottomDistance": 50
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#FF8C42",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "position": "bottom",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "images/icons/home.png",
        "selectedIconPath": "images/icons/home-active.png"
      },
      {
        "pagePath": "pages/category/category",
        "text": "分类",
        "iconPath": "images/icons/category.png", 
        "selectedIconPath": "images/icons/category-active.png"
      },
      {
        "pagePath": "pages/cart/cart",
        "text": "购物车",
        "iconPath": "images/icons/cart.png",
        "selectedIconPath": "images/icons/cart-active.png"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的",
        "iconPath": "images/icons/profile.png",
        "selectedIconPath": "images/icons/profile-active.png"
      }
    ]
  }
}
```

### 4.2 首页JavaScript核心代码（home.js）

```javascript
// pages/home/<USER>
const app = getApp();
const api = require('../../utils/api.js');

Page({
  data: {
    loading: false,
    primaryColor: '#FF8C42',
    showCustomNavbar: false,
    guideData: {},
    quickActions: [],
    videoList: [],
    timelineData: [],
    recommendProducts: [],
    cultureArticles: []
  },

  onLoad(options) {
    console.log('首页加载', options);
    this.initPageData();
  },

  onShow() {
    console.log('首页显示');
    this.updateCartCount();
    this.refreshPageDataSilently();
  },

  // 初始化页面数据
  async initPageData() {
    this.setData({ loading: true });

    try {
      // 从API获取数据
      const response = await api.request('/api/get_home_data');
      if (response.success) {
        this.setData({
          guideData: response.data.guide_data || {},
          quickActions: response.data.quick_actions || [],
          videoList: response.data.video_list || [],
          timelineData: response.data.timeline_data || [],
          recommendProducts: response.data.recommend_products || []
        });
      }
    } catch (error) {
      console.error('加载首页数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 快捷入口点击处理
  onQuickActionTap(e) {
    const action = e.currentTarget.dataset.action;
    switch (action) {
      case 'craft':
        wx.navigateTo({
          url: '/pages/culture/culture'
        });
        break;
      case 'category':
        wx.switchTab({
          url: '/pages/category/category'
        });
        break;
      case 'custom':
        wx.showToast({
          title: '定制服务即将开放',
          icon: 'none'
        });
        break;
    }
  },

  // 视频播放处理
  onVideoPlay(e) {
    const videoId = e.currentTarget.dataset.id;
    const videoList = this.data.videoList.map(video => ({
      ...video,
      showPlayer: video.id === videoId
    }));
    this.setData({ videoList });
  }
});
```

### 4.3 Flask后端核心代码（app.py）

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漆器文化商城小程序 - 后台管理系统
Flask应用主文件
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
import os
import json
import uuid
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'lacquerware_admin_secret_key_2024'

# 启用CORS支持，允许小程序跨域访问
CORS(app, origins=['*'], methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
ALLOWED_VIDEO_EXTENSIONS = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
MAX_VIDEO_SIZE = 50 * 1024 * 1024  # 50MB

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_VIDEO_SIZE

# 数据文件路径
FRONTEND_DATA_FILE = '../frontend/data/miniprogram_data.json'
DATA_FILE = 'data/miniprogram_data.json'

def load_miniprogram_data():
    """加载小程序数据"""
    try:
        if os.path.exists(FRONTEND_DATA_FILE):
            with open(FRONTEND_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif os.path.exists(DATA_FILE):
            with open(DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载数据失败: {e}")

    # 返回默认数据
    return {
        "app_config": {
            "app_name": "非物质文化遗产·漆器",
            "primary_color": "#FF8C42",
            "secondary_color": "#FFF8F0"
        },
        "home_data": {
            "guide_data": [],
            "quick_actions": [],
            "video_list": [],
            "timeline_data": [],
            "recommend_products": []
        }
    }

def save_miniprogram_data(data):
    """保存小程序数据到前端和后端"""
    try:
        # 保存到前端数据文件
        os.makedirs(os.path.dirname(FRONTEND_DATA_FILE), exist_ok=True)
        with open(FRONTEND_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        # 同时保存到后端数据文件作为备份
        os.makedirs(os.path.dirname(DATA_FILE), exist_ok=True)
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

# API路由
@app.route('/api/get_home_data')
def get_home_data():
    """获取首页数据API"""
    try:
        data = load_miniprogram_data()
        return jsonify({
            'success': True,
            'data': data.get('home_data', {})
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取数据失败: {str(e)}'
        })

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """上传文件API - 支持单个或多个文件上传"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有选择文件'})

        files = request.files.getlist('file')
        uploaded_files = []

        # 确保目标目录存在
        images_dir = os.path.join(os.path.dirname(__file__), 'uploads/images')
        os.makedirs(images_dir, exist_ok=True)

        for file in files:
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # 生成唯一文件名
                name, ext = os.path.splitext(filename)
                unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"

                # 保存文件
                save_path = os.path.join(images_dir, unique_filename)
                file.save(save_path)

                uploaded_files.append({
                    'original_name': filename,
                    'filename': unique_filename,
                    'path': f"/images/{unique_filename}"
                })

        return jsonify({
            'success': True,
            'message': f"成功上传 {len(uploaded_files)} 个文件",
            'uploaded_files': uploaded_files
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传过程中发生错误: {str(e)}'
        })

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
```

### 4.4 首页WXML模板代码（home.wxml）

```xml
<!--pages/home/<USER>
<view class="home-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" wx:if="{{showCustomNavbar}}">
    <view class="navbar-content">
      <text class="navbar-title">非物质文化遗产·漆器</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <scroll-view class="main-content" scroll-y="true" wx:else>
    <!-- 指南卡片 -->
    <view class="guide-section">
      <view class="guide-card" wx:for="{{guideData}}" wx:key="id">
        <image class="guide-cover" src="{{item.cover_image}}" mode="aspectFill"></image>
        <view class="guide-info">
          <text class="guide-title">{{item.title}}</text>
          <text class="guide-expert">{{item.expert_name}}</text>
          <text class="guide-date">{{item.publish_date}}</text>
        </view>
      </view>
    </view>

    <!-- 快捷入口 -->
    <view class="quick-actions">
      <view class="action-item"
            wx:for="{{quickActions}}"
            wx:key="id"
            data-action="{{item.action}}"
            bindtap="onQuickActionTap">
        <text class="action-icon">{{item.icon}}</text>
        <text class="action-text">{{item.text}}</text>
      </view>
    </view>

    <!-- 视频列表 -->
    <view class="video-section">
      <view class="section-title">文化视频</view>
      <view class="video-list">
        <view class="video-item" wx:for="{{videoList}}" wx:key="id">
          <view class="video-cover"
                data-id="{{item.id}}"
                bindtap="onVideoPlay">
            <image src="{{item.thumbnail}}" mode="aspectFill"></image>
            <view class="play-button">▶</view>
            <text class="video-duration">{{item.duration}}</text>
          </view>
          <text class="video-title">{{item.title}}</text>

          <!-- 视频播放器 -->
          <video wx:if="{{item.showPlayer}}"
                 src="{{item.url}}"
                 poster="{{item.thumbnail}}"
                 controls
                 class="video-player">
          </video>
        </view>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section">
      <view class="section-title">推荐商品</view>
      <view class="product-grid">
        <view class="product-item"
              wx:for="{{recommendProducts}}"
              wx:key="id"
              data-id="{{item.id}}"
              bindtap="onProductTap">
          <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-name">{{item.name}}</text>
            <text class="product-price">¥{{item.price}}</text>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
```

### 4.5 首页WXSS样式代码（home.wxss）

```css
/* pages/home/<USER>/
.home-container {
  min-height: 100vh;
  background-color: var(--cream-white);
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background-color: var(--primary-orange);
  z-index: 1000;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-top: 44rpx;
}

.navbar-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid var(--primary-orange);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.main-content {
  padding: 20rpx;
}

/* 指南卡片样式 */
.guide-section {
  margin-bottom: 40rpx;
}

.guide-card {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.guide-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.guide-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.guide-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.guide-expert {
  font-size: 26rpx;
  color: var(--primary-orange);
  margin-bottom: 8rpx;
}

.guide-date {
  font-size: 24rpx;
  color: #999;
}

/* 快捷入口样式 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  background-color: #f5f5f5;
  transform: scale(0.95);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 26rpx;
  color: #333;
}

/* 视频列表样式 */
.video-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 8rpx;
}

.video-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.video-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.video-cover {
  position: relative;
  width: 100%;
  height: 400rpx;
}

.video-cover image {
  width: 100%;
  height: 100%;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0,0,0,0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
}

.video-duration {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  background: rgba(0,0,0,0.6);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.video-title {
  display: block;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.video-player {
  width: 100%;
  height: 400rpx;
}

/* 推荐商品样式 */
.recommend-section {
  margin-bottom: 40rpx;
}

.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.product-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 28rpx;
  color: var(--primary-orange);
  font-weight: bold;
}
```

**【此处插入前端代码截图】**
**【此处插入后端代码截图】**

## 五、界面设计展示

### 5.1 小程序界面

1. **首页界面**
   - 展示漆器文化指南
   - 快捷功能入口
   - 视频播放区域
   - 推荐商品展示

**【此处插入首页界面截图】**

2. **分类页面**
   - 商品分类展示
   - 分类商品数量
   - 分类图标设计

**【此处插入分类页面截图】**

3. **购物车页面**
   - 商品列表展示
   - 数量调整功能
   - 价格计算显示

**【此处插入购物车页面截图】**

4. **用户中心页面**
   - 用户信息展示
   - 资产统计
   - 功能菜单

**【此处插入用户中心页面截图】**

### 5.2 后台管理界面

1. **管理首页**
   - 数据统计概览
   - 快捷操作入口

**【此处插入管理首页截图】**

2. **内容管理页面**
   - 首页内容编辑
   - 商品信息管理

**【此处插入内容管理页面截图】**

3. **媒体管理页面**
   - 图片上传管理
   - 视频文件管理

**【此处插入媒体管理页面截图】**

## 六、创新特色

### 6.1 文化传承数字化

- 将传统漆器文化与现代数字技术结合
- 通过视频、图文等多媒体形式展示工艺流程
- 建立漆器文化知识库和传承时间线

### 6.2 用户体验优化

- 采用现代化UI设计，符合用户使用习惯
- 响应式布局，适配不同设备屏幕
- 流畅的交互动画和视觉效果

### 6.3 管理系统集成

- 前后端分离架构，便于维护和扩展
- 可视化内容管理，降低运营门槛
- 实时数据同步，确保信息一致性

### 6.4 技术架构先进

- 采用微信小程序原生开发，性能优异
- Flask轻量级后端，部署简单
- JSON数据存储，结构灵活

## 七、软件规模统计

### 7.1 代码行数统计

- 前端JavaScript代码：约2000行
- 前端WXML模板：约800行
- 前端WXSS样式：约1200行
- 后端Python代码：约1800行
- 后端HTML模板：约600行
- 配置文件：约200行
- **总计：约6600行代码**

### 7.2 文件数量统计

- 前端页面文件：20个
- 后端Python文件：1个
- 模板文件：10个
- 配置文件：5个
- 静态资源文件：50+个
- **总计：85+个文件**

## 八、软件截图说明

本文档中需要插入以下截图位置：

1. **【系统架构图】** - 第三章技术架构部分
2. **【后端代码截图】** - 第四章核心代码展示部分
3. **【首页界面截图】** - 第五章界面设计展示部分
4. **【分类页面截图】** - 第五章界面设计展示部分
5. **【购物车页面截图】** - 第五章界面设计展示部分
6. **【用户中心页面截图】** - 第五章界面设计展示部分
7. **【管理首页截图】** - 第五章界面设计展示部分
8. **【内容管理页面截图】** - 第五章界面设计展示部分
9. **【媒体管理页面截图】** - 第五章界面设计展示部分

## 九、完整源代码清单

### 9.1 前端小程序源代码文件列表

#### 9.1.1 应用配置文件
- `app.js` - 应用入口文件
- `app.json` - 应用配置文件
- `app.wxss` - 全局样式文件
- `project.config.json` - 项目配置文件

#### 9.1.2 页面文件
**首页 (pages/home/<USER>
- `home.js` - 首页逻辑文件
- `home.wxml` - 首页模板文件
- `home.wxss` - 首页样式文件
- `home.json` - 首页配置文件

**分类页 (pages/category/)**
- `category.js` - 分类页逻辑文件
- `category.wxml` - 分类页模板文件
- `category.wxss` - 分类页样式文件
- `category.json` - 分类页配置文件

**购物车 (pages/cart/)**
- `cart.js` - 购物车逻辑文件
- `cart.wxml` - 购物车模板文件
- `cart.wxss` - 购物车样式文件
- `cart.json` - 购物车配置文件

**用户中心 (pages/profile/)**
- `profile.js` - 用户中心逻辑文件
- `profile.wxml` - 用户中心模板文件
- `profile.wxss` - 用户中心样式文件
- `profile.json` - 用户中心配置文件

**商品详情 (pages/product-detail/)**
- `product-detail.js` - 商品详情逻辑文件
- `product-detail.wxml` - 商品详情模板文件
- `product-detail.wxss` - 商品详情样式文件
- `product-detail.json` - 商品详情配置文件

#### 9.1.3 工具和组件文件
- `utils/api.js` - API请求工具
- `utils/util.js` - 通用工具函数
- `components/` - 自定义组件目录

#### 9.1.4 静态资源文件
- `images/` - 图片资源目录
- `data/miniprogram_data.json` - 数据配置文件

### 9.2 后端服务源代码文件列表

#### 9.2.1 主要Python文件
- `app.py` - Flask主应用文件（1804行）
- `requirements.txt` - Python依赖包配置

#### 9.2.2 模板文件 (templates/)
- `index.html` - 管理后台首页模板
- `content.html` - 内容管理页面模板
- `images.html` - 图片管理页面模板
- `videos.html` - 视频管理页面模板
- `settings.html` - 系统设置页面模板
- `products.html` - 商品管理页面模板
- `culture.html` - 文化内容管理页面模板

#### 9.2.3 静态资源文件 (static/)
- `css/` - 样式文件目录
- `js/` - JavaScript文件目录
- `uploads/` - 文件上传目录

#### 9.2.4 数据文件
- `data/miniprogram_data.json` - 小程序数据文件

### 9.3 核心功能代码统计

| 模块 | 文件数 | 代码行数 | 主要功能 |
|------|--------|----------|----------|
| 前端小程序 | 25+ | 4000+ | 用户界面、交互逻辑 |
| 后端服务 | 10+ | 2600+ | API服务、数据管理 |
| 配置文件 | 5 | 200+ | 项目配置、依赖管理 |
| **总计** | **40+** | **6800+** | **完整应用系统** |

### 9.4 技术实现特色

#### 9.4.1 前端技术特色
- 采用微信小程序原生开发框架
- 响应式设计，适配不同屏幕尺寸
- 组件化开发，代码复用性高
- 数据驱动的视图更新机制
- 优雅的动画和交互效果

#### 9.4.2 后端技术特色
- Flask轻量级Web框架
- RESTful API设计规范
- 文件上传和静态文件服务
- JSON数据存储，结构灵活
- 跨域资源共享(CORS)支持

#### 9.4.3 系统集成特色
- 前后端分离架构
- 实时数据同步机制
- 统一的错误处理和日志记录
- 可扩展的模块化设计

## 十、附录

### 10.1 开发环境
- **开发工具**：微信开发者工具、Visual Studio Code
- **版本控制**：Git
- **测试环境**：微信小程序开发版
- **部署环境**：云服务器
- **开发语言**：JavaScript、Python、HTML、CSS

### 10.2 第三方依赖
**Python后端依赖：**
- Flask==2.3.3 - Web框架
- Flask-CORS==4.0.0 - 跨域支持
- Werkzeug==2.3.7 - WSGI工具库
- Jinja2==3.1.2 - 模板引擎
- MarkupSafe==2.1.3 - 安全标记
- itsdangerous==2.1.2 - 数据签名
- click==8.1.7 - 命令行工具
- blinker==1.6.3 - 信号系统

**前端小程序依赖：**
- 微信小程序基础库
- 微信开发者工具

### 10.3 软件著作权申请材料清单

1. **软件著作权登记申请表** ✓
2. **申请人身份证明文件** ✓
3. **软件源程序代码** ✓（本文档包含）
4. **软件说明书** ✓（本文档）
5. **软件界面截图** ✓（需要插入）
6. **其他相关证明材料** ✓

---

**申请人声明：**
本人保证以上所填写的内容真实、准确，所提交的软件为原创作品，不存在抄袭、剽窃等侵犯他人著作权的行为。本软件系统完全由本人独立开发完成，拥有完整的知识产权。如有虚假，愿承担相应的法律责任。

**申请日期：** 2024年 __ 月 __ 日
**申请人签名：** ________________
