// app.js
const api = require('./utils/api.js');
const envConfig = require('./config/env.js');

App({
  globalData: {
    userInfo: null,
    cartItems: [],
    totalCartCount: 0,
    systemInfo: null,
    // 应用配置 - 从环境配置文件加载
    config: envConfig.getFullConfig(),
    // 用户数据
    user: {
      isLogin: false,
      userInfo: {
        nickName: '张三',
        avatarUrl: '/images/avatar.jpg',
        phone: '138****8888',
        level: 'VIP',
        points: 1280
      },
      assets: {
        coupons: 3,
        points: 1280,
        balance: 128.50,
        cards: 2
      }
    },
    // 数据刷新标记
    dataRefreshFlags: {
      home: false,
      category: false,
      user: false
    }
  },

  onLaunch(options) {
    console.log('小程序启动', options);

    // 获取系统信息
    this.getSystemInfo();

    // 初始化购物车数据
    this.initCartData();

    // 检查更新
    this.checkForUpdate();

    // 初始化用户信息
    this.initUserInfo();

    // 初始化应用配置
    this.initAppConfig();
  },

  onShow(options) {
    console.log('小程序显示', options);
  },

  onHide() {
    console.log('小程序隐藏');
    // 保存购物车数据
    this.saveCartData();
  },

  onError(msg) {
    console.error('小程序错误:', msg);
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      },
      fail: (err) => {
        console.error('获取系统信息失败:', err);
      }
    });
  },

  // 初始化购物车数据
  initCartData() {
    try {
      const cartData = wx.getStorageSync('cartItems');
      if (cartData) {
        this.globalData.cartItems = cartData;
        this.updateCartCount();
      } else {
        // 初始化示例数据
        this.globalData.cartItems = [
          {
            id: 1,
            name: '传统漆扇 · 牡丹花开',
            price: 288.00,
            quantity: 1,
            image: '/images/product1.jpg',
            selected: true
          },
          {
            id: 2,
            name: '精美手镯盒 · 凤凰于飞',
            price: 168.00,
            quantity: 2,
            image: '/images/product2.jpg',
            selected: true
          }
        ];
        this.updateCartCount();
        this.saveCartData();
      }
    } catch (e) {
      console.error('初始化购物车数据失败:', e);
      this.globalData.cartItems = [];
    }
  },

  // 保存购物车数据
  saveCartData() {
    try {
      wx.setStorageSync('cartItems', this.globalData.cartItems);
    } catch (e) {
      console.error('保存购物车数据失败:', e);
    }
  },

  // 更新购物车数量
  updateCartCount() {
    const totalCount = this.globalData.cartItems.reduce((sum, item) => {
      return sum + (item.selected ? item.quantity : 0);
    }, 0);
    this.globalData.totalCartCount = totalCount;

    // 更新tabBar徽章
    if (totalCount > 0) {
      wx.setTabBarBadge({
        index: 2, // 购物车tab的索引
        text: totalCount.toString()
      });
    } else {
      wx.removeTabBarBadge({
        index: 2
      });
    }
  },

  // 获取购物车数量
  getCartCount() {
    return this.globalData.totalCartCount || 0;
  },

  // 添加商品到购物车
  addToCart(product) {
    const existingItem = this.globalData.cartItems.find(item => item.id === product.id);
    
    if (existingItem) {
      existingItem.quantity += 1;
    } else {
      this.globalData.cartItems.push({
        ...product,
        quantity: 1,
        selected: true
      });
    }
    
    this.updateCartCount();
    this.saveCartData();
    
    // 显示添加成功提示
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success',
      duration: 1500
    });
  },

  // 从购物车移除商品
  removeFromCart(productId) {
    this.globalData.cartItems = this.globalData.cartItems.filter(item => item.id !== productId);
    this.updateCartCount();
    this.saveCartData();
  },

  // 更新购物车商品数量
  updateCartItemQuantity(productId, quantity) {
    const item = this.globalData.cartItems.find(item => item.id === productId);
    if (item) {
      item.quantity = Math.max(1, quantity);
      this.updateCartCount();
      this.saveCartData();
    }
  },

  // 切换商品选中状态
  toggleCartItemSelected(productId) {
    const item = this.globalData.cartItems.find(item => item.id === productId);
    if (item) {
      item.selected = !item.selected;
      this.updateCartCount();
      this.saveCartData();
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate);
      });
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  // 初始化用户信息
  initUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.globalData.user.userInfo = userInfo;
        this.globalData.user.isLogin = true;
      }
    } catch (e) {
      console.error('获取用户信息失败:', e);
    }
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          console.log('登录成功:', res.code);
          // 这里应该调用后端接口进行登录验证
          resolve(res);
        },
        fail: (err) => {
          console.error('登录失败:', err);
          reject(err);
        }
      });
    });
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.globalData.user.userInfo = res.userInfo;
          this.globalData.user.isLogin = true;
          
          // 保存用户信息
          try {
            wx.setStorageSync('userInfo', res.userInfo);
          } catch (e) {
            console.error('保存用户信息失败:', e);
          }
          
          resolve(res.userInfo);
        },
        fail: (err) => {
          console.error('获取用户信息失败:', err);
          reject(err);
        }
      });
    });
  },

  // 工具函数：格式化价格
  formatPrice(price) {
    return `¥${parseFloat(price).toFixed(2)}`;
  },

  // 工具函数：格式化日期
  formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day);
  },

  // 工具函数：显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    });
  },

  // 工具函数：隐藏加载提示
  hideLoading() {
    wx.hideLoading();
  },

  // 工具函数：显示提示信息
  showToast(title, icon = 'none', duration = 1500) {
    wx.showToast({
      title: title,
      icon: icon,
      duration: duration
    });
  },

  // 获取当前环境配置
  getCurrentConfig() {
    return envConfig.getCurrentEnvConfig();
  },

  // 获取API基础URL
  getApiUrl(path = '') {
    return envConfig.getApiUrl(path);
  },

  // 获取图片基础URL
  getImageUrl(imagePath = '') {
    return envConfig.getImageUrl(imagePath);
  },

  // 获取视频基础URL
  getVideoUrl(videoPath = '') {
    return envConfig.getVideoUrl(videoPath);
  },

  // 获取上传URL
  getUploadUrl() {
    return this.getCurrentConfig().uploadUrl;
  },

  // 构建完整的图片URL（兼容旧方法）
  buildImageUrl(imagePath) {
    return this.getImageUrl(imagePath);
  },

  // 构建完整的视频URL（兼容旧方法）
  buildVideoUrl(videoPath) {
    return this.getVideoUrl(videoPath);
  },

  // 检查是否为开发环境
  isDevelopment() {
    return envConfig.isDevelopment();
  },

  // 检查是否为生产环境
  isProduction() {
    return envConfig.isProduction();
  },

  // 切换环境（开发时使用）
  switchEnvironment(newEnv) {
    if (envConfig.ENV_CONFIG[newEnv]) {
      // 更新环境配置
      envConfig.ENV_CONFIG.current = newEnv;
      this.globalData.config = envConfig.getFullConfig();

      console.log(`已切换到${newEnv}环境:`, this.getCurrentConfig());

      // 清除API缓存，强制重新获取数据
      if (api && api.clearAllCache) {
        api.clearAllCache();
      }

      // 显示提示
      const envName = newEnv === 'development' ? '开发' :
                     newEnv === 'production' ? '生产' : '测试';
      this.showToast(`已切换到${envName}环境`);
    }
  },

  // 智能处理数据中的URL（支持多种localhost格式）
  processDataUrls(data) {
    if (!data) return data;

    // 递归处理对象中的URL
    const processObject = (obj) => {
      if (Array.isArray(obj)) {
        return obj.map(item => processObject(item));
      } else if (obj && typeof obj === 'object') {
        const newObj = {};
        for (const key in obj) {
          newObj[key] = processObject(obj[key]);
        }
        return newObj;
      } else if (typeof obj === 'string' && obj.startsWith('http')) {
        // 使用智能映射处理URL
        return envConfig.mapUrlToCurrentEnvironment(obj);
      }
      return obj;
    };

    return processObject(data);
  },

  // 智能处理单个图片URL
  processImageUrl(imageUrl) {
    return this.getImageUrl(imageUrl);
  },

  // 智能处理单个视频URL
  processVideoUrl(videoUrl) {
    return this.getVideoUrl(videoUrl);
  },

  // 批量处理图片URL数组
  processImageUrls(imageUrls) {
    if (!Array.isArray(imageUrls)) return imageUrls;
    return imageUrls.map(url => this.processImageUrl(url));
  },

  // 标记数据需要刷新
  markDataForRefresh(dataType) {
    if (this.globalData.dataRefreshFlags.hasOwnProperty(dataType)) {
      this.globalData.dataRefreshFlags[dataType] = true;
      // 清除对应的缓存
      api.clearCache(dataType);
      console.log(`标记${dataType}数据需要刷新`);
    }
  },

  // 检查数据是否需要刷新
  shouldRefreshData(dataType) {
    return this.globalData.dataRefreshFlags[dataType] || false;
  },

  // 清除数据刷新标记
  clearRefreshFlag(dataType) {
    if (this.globalData.dataRefreshFlags.hasOwnProperty(dataType)) {
      this.globalData.dataRefreshFlags[dataType] = false;
    }
  },

  // 初始化应用配置
  async initAppConfig() {
    try {
      const config = await api.getAppConfig();
      if (config) {
        // 更新全局配置
        this.globalData.config = {
          ...this.globalData.config,
          ...config
        };

        // 动态更新主题色
        this.updateThemeColors(config);

        console.log('应用配置初始化成功:', config);
      }
    } catch (error) {
      console.warn('应用配置初始化失败:', error);
    }
  },

  // 更新主题色
  updateThemeColors(config) {
    if (!config.primary_color && !config.secondary_color) {
      return;
    }

    try {
      // 动态设置CSS变量
      const style = `
        page {
          --primary-orange: ${config.primary_color || '#FF8C42'} !important;
          --cream-white: ${config.secondary_color || '#FFF8F0'} !important;
        }
      `;

      // 创建或更新style标签
      let styleElement = wx.createSelectorQuery().select('#dynamic-theme-style');
      if (!styleElement) {
        // 注入动态样式
        wx.loadFontFace({
          family: 'dynamic-theme',
          source: `data:text/css;base64,${btoa(style)}`,
          success: () => {
            console.log('主题色更新成功');
          },
          fail: (err) => {
            console.warn('主题色更新失败:', err);
          }
        });
      }

      // 更新TabBar颜色
      if (config.primary_color) {
        wx.setTabBarStyle({
          selectedColor: config.primary_color
        });
      }

    } catch (error) {
      console.warn('更新主题色失败:', error);
    }
  },

  // 全局数据刷新（当后台管理系统更新数据时调用）
  refreshAllData() {
    // 标记所有数据需要刷新
    Object.keys(this.globalData.dataRefreshFlags).forEach(key => {
      this.markDataForRefresh(key);
    });

    // 清除所有缓存
    api.clearAllCache();

    // 重新初始化应用配置
    this.initAppConfig();

    console.log('已标记所有数据需要刷新');

    // 可以发送事件通知各个页面刷新
    wx.showToast({
      title: '数据已更新',
      icon: 'success'
    });
  }
});
