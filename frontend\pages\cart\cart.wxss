/* pages/cart/cart.wxss */

/* 购物车页面容器 */
.cart-container {
  padding: var(--spacing-md);
  background-color: var(--cream-white);
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为底部结算栏留出更多空间 */
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-dark);
}

/* 购物车内容 */
.cart-content {
  min-height: calc(100vh - 200rpx);
}

/* 购物车商品列表 */
.cart-items {
  margin-bottom: var(--spacing-lg);
}

.cart-item {
  padding: 28rpx;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，避免内容挤压 */
  gap: var(--spacing-md);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 140, 66, 0.1);
  min-height: 160rpx; /* 确保有足够高度 */
}

.cart-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 28rpx rgba(0, 0, 0, 0.1);
}

/* 选择框 */
.item-checkbox {
  flex-shrink: 0;
  margin-top: 8rpx; /* 稍微向下偏移，与商品信息对齐 */
}

.checkbox {
  width: 44rpx;
  height: 44rpx;
  border: 3rpx solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.checkbox::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-orange) 0%, #ff7a2e 100%);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.checkbox.checked {
  border-color: var(--primary-orange);
}

.checkbox.checked::before {
  opacity: 1;
  transform: scale(1);
}

.checkbox-icon {
  color: white;
  font-size: 26rpx;
  font-weight: bold;
  z-index: 1;
  position: relative;
}

/* 商品图片 */
.item-image {
  width: 130rpx;
  height: 130rpx;
  border-radius: var(--radius-medium);
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(255, 140, 66, 0.1);
}

.item-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 140, 66, 0.05) 100%);
  z-index: 1;
}

.item-image image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.cart-item:active .item-image image {
  transform: scale(1.05);
}

/* 商品信息 */
.item-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding-top: 4rpx; /* 稍微向下偏移，与选择框对齐 */
}

.item-name {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-price {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--primary-orange);
}

/* 商品控制区域 */
.item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx; /* 增加顶部间距 */
  padding-top: 8rpx; /* 增加内边距 */
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 12rpx; /* 稍微增加间距 */
}

.quantity-btn {
  width: 64rpx;
  height: 64rpx;
  border: 2rpx solid var(--primary-orange);
  background: white;
  border-radius: var(--radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--primary-orange);
  padding: 0;
  margin: 0;
  line-height: 1;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(255, 140, 66, 0.15);
  transition: all 0.2s ease;
}

.quantity-btn:disabled {
  opacity: 0.4;
  background: #f5f5f5;
  border-color: var(--border-color);
  color: var(--text-light);
  box-shadow: none;
}

.quantity-btn.primary {
  background: var(--primary-orange);
  border-color: var(--primary-orange);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(255, 140, 66, 0.3);
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-btn.primary:active {
  background: var(--primary-orange-dark);
  border-color: var(--primary-orange-dark);
}

.quantity-btn::after {
  border: none;
}

.quantity-input {
  width: 100rpx;
  height: 64rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-small);
  text-align: center;
  font-size: 28rpx;
  background: white;
  font-weight: 600;
  color: var(--text-dark);
}

.delete-btn {
  width: 64rpx;
  height: 64rpx;
  background: #ff4757;
  border: none;
  border-radius: var(--radius-small);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  padding: 0;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
  transition: all 0.2s ease;
}

.delete-btn:active {
  transform: scale(0.95);
  background: #e63946;
}

.delete-btn::after {
  border: none;
}

/* 购物车空状态 */
.cart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  min-height: 400rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-gray);
  margin-bottom: var(--spacing-lg);
}

.go-shopping-btn {
  background: var(--primary-orange);
  color: white;
  border: none;
  border-radius: var(--radius-medium);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 32rpx;
  font-weight: 500;
}

.go-shopping-btn::after {
  border: none;
}

/* 购物车底部结算栏 */
.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx var(--spacing-md);
  border-top: 2rpx solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  box-shadow: 0 -4rpx 20rpx var(--shadow-light);
  z-index: 100;
  min-height: 100rpx; /* 确保有足够高度 */
}

/* 全选 */
.select-all {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.select-all-text {
  font-size: 28rpx;
  color: var(--text-dark);
}

/* 合计信息 */
.total-section {
  flex: 1;
  text-align: right;
}

.total-info {
  margin-bottom: var(--spacing-xs);
}

.total-label {
  font-size: 28rpx;
  color: var(--text-gray);
}

.total-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--primary-orange);
  margin-left: var(--spacing-sm);
}

.selected-count {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 结算按钮 */
.checkout-btn {
  background: linear-gradient(135deg, var(--primary-orange) 0%, #ff7a2e 100%);
  color: white;
  border: none;
  border-radius: var(--radius-medium);
  padding: 20rpx var(--spacing-lg);
  font-size: 30rpx;
  font-weight: 600;
  flex-shrink: 0;
  min-width: 160rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(255, 140, 66, 0.3);
  transition: all 0.3s ease;
}

.checkout-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(255, 140, 66, 0.4);
}

.checkout-btn.disabled {
  background: var(--text-light) !important;
  color: white !important;
  box-shadow: none !important;
  transform: none !important;
}

.checkout-btn::after {
  border: none;
}

/* 删除确认弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--radius-large);
  margin: var(--spacing-lg);
  max-width: 600rpx;
  width: 100%;
  overflow: hidden;
}

.modal-header {
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
  text-align: center;
  border-bottom: 2rpx solid var(--border-color);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
}

.modal-body {
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: center;
}

.modal-text {
  font-size: 28rpx;
  color: var(--text-gray);
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid var(--border-color);
}

.modal-btn {
  flex: 1;
  padding: var(--spacing-lg);
  font-size: 32rpx;
  border: none;
  background: white;
  color: var(--text-dark);
}

.modal-btn.cancel {
  border-right: 2rpx solid var(--border-color);
  color: var(--text-gray);
}

.modal-btn.confirm {
  color: #ff4757;
  font-weight: 500;
}

.modal-btn::after {
  border: none;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .cart-item {
    padding: 20rpx;
    gap: 16rpx;
  }

  .item-image {
    width: 110rpx;
    height: 110rpx;
  }

  .item-name {
    font-size: 26rpx;
  }

  .item-price {
    font-size: 26rpx;
  }

  .quantity-btn {
    width: 56rpx;
    height: 56rpx;
    font-size: 28rpx;
  }

  .quantity-input {
    width: 80rpx;
    height: 56rpx;
    font-size: 24rpx;
  }

  .delete-btn {
    width: 56rpx;
    height: 56rpx;
    font-size: 24rpx;
  }

  .cart-footer {
    padding: 16rpx var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .checkout-btn {
    min-width: 140rpx;
    font-size: 28rpx;
    padding: 16rpx var(--spacing-md);
    height: 64rpx;
  }

  .total-amount {
    font-size: 32rpx;
  }

  .selected-count {
    font-size: 22rpx;
  }
}