#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版打包脚本
一键将Flask应用打包成exe
"""

import os
import sys
import subprocess
import shutil

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"正在{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True, encoding='utf-8')
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def main():
    print("漆器商城后端服务打包工具")
    print("=" * 40)
    
    # 检查必要文件
    if not os.path.exists('app.py'):
        print("❌ 未找到app.py文件")
        return False
    
    if not os.path.exists('requirements.txt'):
        print("❌ 未找到requirements.txt文件")
        return False
    
    # 安装PyInstaller
    if not run_command("pip install pyinstaller", "安装PyInstaller"):
        return False
    
    # 安装依赖
    if not run_command("pip install -r requirements.txt", "安装项目依赖"):
        return False
    
    # 打包命令
    pack_command = [
        "pyinstaller",
        "--onefile",  # 打包成单个文件
        "--console",  # 显示控制台
        "--name", "漆器商城后端服务",
        "--add-data", "templates;templates",
        "--add-data", "static;static", 
        "--add-data", "data;data",
        "--add-data", "uploads;uploads",
        "app.py"
    ]
    
    if not run_command(" ".join(pack_command), "打包exe文件"):
        return False
    
    # 创建启动脚本
    startup_bat = '''@echo off
chcp 65001 > nul
echo 漆器商城后端服务
echo ========================
echo 正在启动服务...
echo.
echo 访问地址: http://127.0.0.1:5000
echo 管理后台: http://127.0.0.1:5000
echo.
echo 按 Ctrl+C 停止服务
echo ========================
echo.

"漆器商城后端服务.exe"

echo.
echo 服务已停止
pause
'''

    with open('dist/启动服务.bat', 'w', encoding='utf-8') as f:
        f.write(startup_bat)
    
    # 创建使用说明
    readme = '''漆器商城后端服务 - 使用说明
================================

📦 文件说明：
- 漆器商城后端服务.exe  主程序
- 启动服务.bat         一键启动脚本
- 使用说明.txt         本文件

🚀 启动方法：
双击"启动服务.bat"文件即可启动服务

🌐 访问地址：
- 管理后台：http://127.0.0.1:5000
- API接口：http://127.0.0.1:5000/api/

⚠️ 注意事项：
1. 请确保5000端口未被占用
2. 首次启动可能需要较长时间
3. 关闭程序请按Ctrl+C
4. 数据文件会自动创建在程序目录

🔧 故障排除：
- 如启动失败，请检查杀毒软件是否拦截
- 如端口被占用，请关闭其他占用5000端口的程序
- 如有权限问题，请以管理员身份运行

版本：V1.0
'''
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme)
    
    # 清理临时文件
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            os.remove(file)
    
    print("\n打包完成！")
    print("=" * 40)
    print("输出目录: dist/")
    print("主程序: dist/漆器商城后端服务.exe")
    print("启动脚本: dist/启动服务.bat")
    print("使用说明: dist/使用说明.txt")
    print("=" * 40)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n打包失败")
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n发生错误: {e}")
        input("按回车键退出...")
