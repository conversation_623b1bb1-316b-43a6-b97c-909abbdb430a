{% extends "base.html" %}

{% block title %}商品管理 - 漆器文化商城管理后台{% endblock %}

{% block page_title %}商品管理{% endblock %}

{% block content %}
<!-- 操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <button class="btn btn-primary" onclick="showAddProductModal()">
            <i class="fas fa-plus me-1"></i>添加商品
        </button>
        <button class="btn btn-outline-secondary" onclick="refreshProducts()">
            <i class="fas fa-sync-alt me-1"></i>刷新
        </button>
    </div>
    <div class="input-group" style="width: 300px;">
        <input type="text" class="form-control" placeholder="搜索商品..." id="searchInput">
        <button class="btn btn-outline-secondary" type="button">
            <i class="fas fa-search"></i>
        </button>
    </div>
</div>

<!-- 商品列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-box me-2"></i>
            商品列表 ({{ products|length }} 个商品)
        </h5>
    </div>
    <div class="card-body">
        {% if products %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="80">图片</th>
                            <th>商品名称</th>
                            <th width="100">分类</th>
                            <th width="100">价格</th>
                            <th width="80">库存</th>
                            <th width="80">销量</th>
                            <th width="80">评分</th>
                            <th width="100">状态</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>
                                <img src="{{ product.image }}" alt="{{ product.name }}" 
                                     class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                            </td>
                            <td>
                                <strong>{{ product.name }}</strong>
                                <br>
                                <small class="text-muted">{{ product.description[:50] }}...</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ product.category }}</span>
                            </td>
                            <td>
                                <div class="text-danger fw-bold">¥{{ product.price }}</div>
                                {% if product.original_price %}
                                <small class="text-muted text-decoration-line-through">¥{{ product.original_price }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if product.stock > 10 %}bg-success{% elif product.stock > 0 %}bg-warning{% else %}bg-danger{% endif %}">
                                    {{ product.stock }}
                                </span>
                            </td>
                            <td>{{ product.sales }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-1">{{ product.rating }}</span>
                                    <i class="fas fa-star text-warning"></i>
                                </div>
                            </td>
                            <td>
                                <span class="badge {% if product.status == 'active' %}bg-success{% else %}bg-secondary{% endif %}">
                                    {% if product.status == 'active' %}上架{% else %}下架{% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewProduct({{ product.id }})" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="editProduct({{ product.id }})" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteProduct({{ product.id }})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">还没有添加任何商品</h5>
                <p class="text-muted">点击上方的添加按钮开始添加商品</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 添加/编辑商品模态框 -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalTitle">添加商品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <input type="hidden" id="productId" name="id">
                    
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">基本信息</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">商品名称 *</label>
                                <input type="text" class="form-control" id="productName" name="name" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">现价 *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control" id="productPrice" name="price" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">原价</label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control" id="productOriginalPrice" name="original_price" step="0.01">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">分类 *</label>
                                        <select class="form-select" id="productCategory" name="category" required>
                                            <option value="">请选择分类</option>
                                            <option value="fan">漆扇</option>
                                            <option value="box">手镯盒</option>
                                            <option value="pen">笔筒</option>
                                            <option value="tea">茶具</option>
                                            <option value="jewelry">首饰盒</option>
                                            <option value="decoration">装饰品</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" id="productStatus" name="status">
                                            <option value="active">上架</option>
                                            <option value="inactive">下架</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">库存</label>
                                        <input type="number" class="form-control" id="productStock" name="stock" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">销量</label>
                                        <input type="number" class="form-control" id="productSales" name="sales" min="0">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">商品描述</label>
                                <textarea class="form-control" id="productDescription" name="description" rows="3"></textarea>
                            </div>
                        </div>
                        
                        <!-- 详细信息 -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">详细信息</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">主图片 *</label>
                                <input type="text" class="form-control" id="productImage" name="image" placeholder="/images/product.jpg" required>
                                <small class="form-text text-muted">请先在图片管理中上传图片，然后复制路径</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">轮播图片</label>
                                <textarea class="form-control" id="productImages" name="images" rows="4" placeholder="商品详情页轮播图片，每行一个URL"></textarea>
                                <small class="form-text text-muted">
                                    多张图片URL，每行一个，如：<br>
                                    /images/product1.jpg<br>
                                    /images/product1-2.jpg<br>
                                    /images/product1-3.jpg
                                </small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">材料</label>
                                <input type="text" class="form-control" id="productMaterials" name="materials" placeholder="天然漆、实木">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">尺寸</label>
                                        <input type="text" class="form-control" id="productSize" name="size" placeholder="长×宽×高">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">重量</label>
                                        <input type="text" class="form-control" id="productWeight" name="weight" placeholder="约200g">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">产地</label>
                                        <input type="text" class="form-control" id="productOrigin" name="origin" placeholder="福建福州">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">工艺</label>
                                        <input type="text" class="form-control" id="productCraft" name="craft" placeholder="传统手工制作">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">特色功能</label>
                                <input type="text" class="form-control" id="productFeatures" name="features" placeholder="防水防潮,色彩持久,手感舒适">
                                <small class="form-text text-muted">多个特色用逗号分隔</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">商品故事</label>
                                <textarea class="form-control" id="productStory" name="story" rows="4" placeholder="介绍商品的文化背景和制作故事..."></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveProduct()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 商品详情查看模态框 -->
<div class="modal fade" id="productDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">商品详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productDetailContent">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="editProductFromDetail()">编辑商品</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let currentProductId = null;

    // 显示添加商品模态框
    function showAddProductModal() {
        document.getElementById('productModalTitle').textContent = '添加商品';
        document.getElementById('productForm').reset();
        document.getElementById('productId').value = '';
        currentProductId = null;
        new bootstrap.Modal(document.getElementById('productModal')).show();
    }

    // 编辑商品
    function editProduct(productId) {
        // 这里应该从后端获取商品详情
        fetch(`/api/get_product/${productId}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const product = result.data;
                    fillProductForm(product);
                    document.getElementById('productModalTitle').textContent = '编辑商品';
                    currentProductId = productId;
                    new bootstrap.Modal(document.getElementById('productModal')).show();
                } else {
                    showError('获取商品信息失败：' + result.message);
                }
            })
            .catch(error => {
                showError('获取商品信息失败：' + error);
            });
    }

    // 填充商品表单
    function fillProductForm(product) {
        document.getElementById('productId').value = product.id;
        document.getElementById('productName').value = product.name;
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productOriginalPrice').value = product.original_price || '';
        document.getElementById('productCategory').value = product.category;
        document.getElementById('productStatus').value = product.status;
        document.getElementById('productStock').value = product.stock || 0;
        document.getElementById('productSales').value = product.sales || 0;
        document.getElementById('productDescription').value = product.description || '';
        document.getElementById('productImage').value = product.image;

        // 轮播图片
        if (product.images && Array.isArray(product.images)) {
            document.getElementById('productImages').value = product.images.join('\n');
        } else {
            document.getElementById('productImages').value = '';
        }

        // 详细信息
        if (product.detail) {
            document.getElementById('productMaterials').value = product.detail.materials || '';
            document.getElementById('productSize').value = product.detail.size || '';
            document.getElementById('productWeight').value = product.detail.weight || '';
            document.getElementById('productOrigin').value = product.detail.origin || '';
            document.getElementById('productCraft').value = product.detail.craft || '';
            document.getElementById('productFeatures').value = product.detail.features ? product.detail.features.join(',') : '';
            document.getElementById('productStory').value = product.detail.story || '';
        }
    }

    // 保存商品
    function saveProduct() {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);
        
        // 处理特色功能
        const features = formData.get('features');
        if (features) {
            formData.set('features', features.split(',').map(f => f.trim()).filter(f => f));
        }
        
        const data = {};
        for (let [key, value] of formData.entries()) {
            if (key === 'features' && typeof value === 'string') {
                data[key] = value.split(',').map(f => f.trim()).filter(f => f);
            } else if (key === 'images' && typeof value === 'string') {
                // 处理轮播图片，按行分割并过滤空行
                data[key] = value.split('\n').map(img => img.trim()).filter(img => img);
            } else {
                data[key] = value;
            }
        }

        const url = currentProductId ? '/api/update_product' : '/api/add_product';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess(result.message);
                bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showError('保存失败：' + result.message);
            }
        })
        .catch(error => {
            showError('保存失败：' + error);
        });
    }

    // 查看商品详情
    function viewProduct(productId) {
        fetch(`/api/get_product/${productId}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const product = result.data;
                    showProductDetail(product);
                } else {
                    showError('获取商品详情失败：' + result.message);
                }
            })
            .catch(error => {
                showError('获取商品详情失败：' + error);
            });
    }

    // 显示商品详情
    function showProductDetail(product) {
        const content = `
            <div class="row">
                <div class="col-md-4">
                    <img src="${product.image}" alt="${product.name}" class="img-fluid rounded">
                </div>
                <div class="col-md-8">
                    <h4>${product.name}</h4>
                    <p class="text-muted">${product.description || '暂无描述'}</p>
                    
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <strong>价格：</strong>
                            <span class="text-danger fs-5">¥${product.price}</span>
                            ${product.original_price ? `<span class="text-muted text-decoration-line-through ms-2">¥${product.original_price}</span>` : ''}
                        </div>
                        <div class="col-sm-6">
                            <strong>分类：</strong> ${product.category}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <strong>库存：</strong> ${product.stock || 0}
                        </div>
                        <div class="col-sm-6">
                            <strong>销量：</strong> ${product.sales || 0}
                        </div>
                    </div>
                    
                    ${product.detail ? `
                    <div class="mt-4">
                        <h6>详细信息</h6>
                        <ul class="list-unstyled">
                            ${product.detail.materials ? `<li><strong>材料：</strong> ${product.detail.materials}</li>` : ''}
                            ${product.detail.size ? `<li><strong>尺寸：</strong> ${product.detail.size}</li>` : ''}
                            ${product.detail.weight ? `<li><strong>重量：</strong> ${product.detail.weight}</li>` : ''}
                            ${product.detail.origin ? `<li><strong>产地：</strong> ${product.detail.origin}</li>` : ''}
                            ${product.detail.craft ? `<li><strong>工艺：</strong> ${product.detail.craft}</li>` : ''}
                        </ul>
                        
                        ${product.detail.features && product.detail.features.length > 0 ? `
                        <div class="mb-3">
                            <strong>特色功能：</strong>
                            ${product.detail.features.map(f => `<span class="badge bg-primary me-1">${f}</span>`).join('')}
                        </div>
                        ` : ''}
                        
                        ${product.detail.story ? `
                        <div>
                            <strong>商品故事：</strong>
                            <p class="mt-2">${product.detail.story}</p>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        document.getElementById('productDetailContent').innerHTML = content;
        currentProductId = product.id;
        new bootstrap.Modal(document.getElementById('productDetailModal')).show();
    }

    // 从详情页编辑商品
    function editProductFromDetail() {
        bootstrap.Modal.getInstance(document.getElementById('productDetailModal')).hide();
        setTimeout(() => editProduct(currentProductId), 300);
    }

    // 删除商品
    function deleteProduct(productId) {
        if (confirm('确定要删除这个商品吗？删除后无法恢复！')) {
            fetch('/api/delete_product', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({id: productId})
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccess('商品删除成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showError('删除失败：' + result.message);
                }
            })
            .catch(error => {
                showError('删除失败：' + error);
            });
        }
    }

    // 刷新商品列表
    function refreshProducts() {
        location.reload();
    }
</script>
{% endblock %}
