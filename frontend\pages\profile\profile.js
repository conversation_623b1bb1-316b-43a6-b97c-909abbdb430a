// pages/profile/profile.js
const app = getApp();
const api = require('../../utils/api.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    userInfo: {},
    assets: {},
    orderStatusList: [
      {
        type: 'pending_payment',
        icon: '💰',
        text: '待付款',
        count: 2
      },
      {
        type: 'pending_shipment',
        icon: '📦',
        text: '待发货',
        count: 0
      },
      {
        type: 'pending_receipt',
        icon: '🚚',
        text: '待收货',
        count: 1
      },
      {
        type: 'pending_review',
        icon: '⭐',
        text: '待评价',
        count: 0
      },
      {
        type: 'refund_return',
        icon: '🔄',
        text: '退货/售后',
        count: 0
      }
    ],
    functionCards: [
      {
        id: 1,
        type: 'favorites',
        icon: '❤️',
        text: '收藏夹'
      },
      {
        id: 2,
        type: 'footprint',
        icon: '👣',
        text: '足迹'
      },
      {
        id: 3,
        type: 'service',
        icon: '💬',
        text: '客服'
      },
      {
        id: 4,
        type: 'qrcode',
        icon: '📱',
        text: '我的二维码'
      }
    ],
    standaloneFunctions: [
      {
        id: 1,
        type: 'address',
        icon: '📍',
        text: '收货地址'
      },
      {
        id: 2,
        type: 'coupons',
        icon: '🎫',
        text: '优惠券',
        count: '3张'
      },
      {
        id: 3,
        type: 'points_mall',
        icon: '🏪',
        text: '积分商城'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时静默刷新数据
    this.refreshUserDataSilently();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshUserData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '漆器文化商城',
      path: '/pages/home/<USER>'
    };
  },

  /**
   * 编辑用户信息
   */
  onEditProfile() {
    wx.showModal({
      title: '编辑资料',
      content: '跳转到编辑资料页面',
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 资产点击事件
   */
  onAssetTap(e) {
    const type = e.currentTarget.dataset.type;
    const typeMap = {
      coupons: '优惠券',
      points: '积分余额',
      balance: '现金余额',
      cards: '卡券'
    };

    wx.showToast({
      title: `查看${typeMap[type]}`,
      icon: 'none'
    });
  },

  /**
   * 查看全部订单
   */
  onViewAllOrders() {
    wx.showToast({
      title: '跳转到订单列表',
      icon: 'none'
    });
  },

  /**
   * 订单状态点击事件
   */
  onOrderStatusTap(e) {
    const status = e.currentTarget.dataset.status;
    const statusMap = {
      pending_payment: '待付款',
      pending_shipment: '待发货',
      pending_receipt: '待收货',
      pending_review: '待评价',
      refund_return: '退货/售后'
    };

    wx.showToast({
      title: `查看${statusMap[status]}订单`,
      icon: 'none'
    });
  },

  /**
   * 功能卡片点击事件
   */
  onFunctionTap(e) {
    const functionType = e.currentTarget.dataset.function;
    const functionMap = {
      favorites: '收藏夹',
      footprint: '足迹',
      service: '客服',
      qrcode: '我的二维码'
    };

    if (functionType === 'service') {
      wx.makePhoneCall({
        phoneNumber: '************',
        fail: () => {
          wx.showToast({
            title: '拨打客服电话失败',
            icon: 'none'
          });
        }
      });
    } else if (functionType === 'qrcode') {
      wx.showModal({
        title: '我的二维码',
        content: '显示个人二维码',
        showCancel: false
      });
    } else {
      wx.showToast({
        title: `打开${functionMap[functionType]}`,
        icon: 'none'
      });
    }
  },

  /**
   * 独立功能点击事件
   */
  onStandaloneFunctionTap(e) {
    const functionType = e.currentTarget.dataset.function;
    const functionMap = {
      address: '收货地址管理',
      coupons: '优惠券列表',
      points_mall: '积分商城'
    };

    wx.showToast({
      title: `跳转到${functionMap[functionType]}`,
      icon: 'none'
    });
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    this.setData({ loading: true });

    try {
      // 检查网络状态
      const networkStatus = await api.checkNetworkStatus();
      if (!networkStatus.isConnected) {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
        this.loadDefaultUserData();
        this.setData({ loading: false });
        return;
      }

      // 调用API获取用户数据
      const response = await api.get('/api/miniprogram/user');

      if (response.success && response.data) {
        const userData = response.data;

        this.setData({
          userInfo: userData.default_user || {},
          assets: userData.assets || {},
          loading: false
        });

        console.log('用户数据加载成功:', userData);
      } else {
        throw new Error(response.message || '数据格式错误');
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);

      // 显示错误提示
      api.handleError(error, '加载用户数据失败');

      // 加载失败时使用默认数据
      this.loadDefaultUserData();

      this.setData({ loading: false });
    }
  },

  /**
   * 刷新用户数据
   */
  async refreshUserData() {
    try {
      await this.loadUserData();

      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      wx.stopPullDownRefresh();
      api.handleError(error, '刷新失败');
    }
  },

  /**
   * 静默刷新用户数据（不显示loading和提示）
   */
  async refreshUserDataSilently() {
    try {
      // 检查网络状态
      const networkStatus = await api.checkNetworkStatus();
      if (!networkStatus.isConnected) {
        return; // 网络不可用时不刷新
      }

      // 调用API获取最新数据
      const response = await api.get('/api/miniprogram/user', {}, {
        showLoading: false // 不显示loading
      });

      if (response.success && response.data) {
        const userData = response.data;

        // 静默更新数据
        this.setData({
          userInfo: userData.default_user || this.data.userInfo,
          assets: userData.assets || this.data.assets
        });

        console.log('用户数据静默刷新成功');
      }
    } catch (error) {
      // 静默刷新失败时不显示错误提示，只在控制台记录
      console.warn('用户数据静默刷新失败:', error);
    }
  },

  /**
   * 加载默认用户数据（当API调用失败时使用）
   */
  loadDefaultUserData() {
    this.setData({
      userInfo: {
        nickName: '张小明', // 使用较短的名字测试布局
        avatarUrl: '/images/avatar.jpg',
        phone: '138****8888',
        level: '钻石VIP', // 使用较长的等级名称测试布局
        points: 12580 // 使用较大的积分数字测试布局
      },
      assets: {
        coupons: 8,
        points: 12580,
        balance: '1,288.50',
        cards: 5
      }
    });
  },

  /**
   * 初始化用户数据（保留原有方法以兼容）
   */
  initUserData() {
    // 从全局数据获取用户信息
    const globalUser = app.globalData.user;
    if (globalUser) {
      this.setData({
        userInfo: globalUser.userInfo,
        assets: globalUser.assets
      });
    }
  }
})