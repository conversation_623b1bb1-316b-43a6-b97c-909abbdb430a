<!-- pages/category/category.wxml -->
<view class="category-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">商品分类</text>
  </view>

  <!-- 分类列表 -->
  <view class="category-list">
    <view
      class="category-card card"
      wx:for="{{categoryList}}"
      wx:key="id"
      bindtap="onCategoryTap"
      data-category="{{item}}"
    >
      <view class="category-info">
        <text class="category-name">{{item.name}}</text>
        <text class="category-count">{{item.count}}件商品</text>
      </view>
      <view class="category-arrow">→</view>
    </view>
  </view>
</view>