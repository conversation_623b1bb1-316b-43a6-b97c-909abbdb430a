{% extends "base.html" %}

{% block title %}视频管理 - 漆器文化商城管理后台{% endblock %}

{% block page_title %}视频管理{% endblock %}

{% block content %}
<!-- 上传区域 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-video me-2"></i>
            上传视频
        </h5>
    </div>
    <div class="card-body">
        <div class="upload-area" id="videoUploadArea">
            <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
            <h5>拖拽视频到这里或点击上传</h5>
            <p class="text-muted">支持 MP4, AVI, MOV, WMV 格式，最大 50MB</p>
            <input type="file" id="videoFileInput" multiple accept="video/*" style="display: none;">
            <button class="btn btn-primary" onclick="document.getElementById('videoFileInput').click()">
                <i class="fas fa-plus me-1"></i>选择视频
            </button>
        </div>
        
        <!-- 上传进度 -->
        <div id="videoUploadProgress" class="mt-3" style="display: none;">
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted mt-1 d-block">正在上传...</small>
        </div>
    </div>
</div>

<!-- 视频列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-film me-2"></i>
            视频库 ({{ videos|length }} 个视频)
        </h5>
        <div class="btn-group">
            <a href="/video-test" class="btn btn-sm btn-info" title="测试视频播放">
                <i class="fas fa-play-circle"></i> 测试播放
            </a>
            <button class="btn btn-sm btn-success" onclick="refreshVideoConfig()" title="刷新视频配置">
                <i class="fas fa-sync-alt"></i> 刷新配置
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="toggleVideoView('grid')" id="gridVideoViewBtn">
                <i class="fas fa-th"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary active" onclick="toggleVideoView('list')" id="listVideoViewBtn">
                <i class="fas fa-list"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if videos %}
            <!-- 网格视图 -->
            <div id="gridVideoView" class="row" style="display: none;">
                {% for video in videos %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card video-card">
                        <div class="video-container">
                            <video class="card-img-top" controls preload="metadata">
                                <source src="{{ video.path }}" type="video/mp4">
                                您的浏览器不支持视频播放
                            </video>
                            <div class="video-overlay">
                                <button class="btn btn-sm btn-primary" onclick="copyVideoPath('{{ video.path }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteVideo('{{ video.filename }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-2">
                            <h6 class="card-title mb-1">{{ video.filename }}</h6>
                            <small class="text-muted">{{ "%.1f"|format(video.size / 1024 / 1024) }} MB</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- 列表视图 -->
            <div id="listVideoView" class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="200">预览</th>
                            <th>文件名</th>
                            <th>路径</th>
                            <th width="100">大小</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for video in videos %}
                        <tr>
                            <td>
                                <video class="video-preview" controls preload="metadata">
                                    <source src="{{ video.path }}" type="video/mp4">
                                </video>
                            </td>
                            <td>
                                <strong>{{ video.filename }}</strong>
                            </td>
                            <td>
                                <code class="text-muted">{{ video.path }}</code>
                                <button class="btn btn-sm btn-link p-0 ms-2" onclick="copyVideoPath('{{ video.path }}')" title="复制路径">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ "%.1f"|format(video.size / 1024 / 1024) }} MB</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="previewVideo('{{ video.path }}', '{{ video.filename }}')" title="预览">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="copyVideoPath('{{ video.path }}')" title="复制路径">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteVideo('{{ video.filename }}')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-film fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">还没有上传任何视频</h5>
                <p class="text-muted">点击上方的上传按钮开始添加视频</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 视频预览模态框 -->
<div class="modal fade" id="videoPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewVideoModalTitle">视频预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <video id="previewVideo" controls style="max-width: 100%; max-height: 70vh;">
                    <source src="" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="copyVideoPath(document.getElementById('previewVideo').querySelector('source').src)">
                    <i class="fas fa-copy me-1"></i>复制路径
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 视频文件上传处理
    const videoFileInput = document.getElementById('videoFileInput');
    const videoUploadArea = document.getElementById('videoUploadArea');
    const videoUploadProgress = document.getElementById('videoUploadProgress');

    // 拖拽上传
    videoUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        videoUploadArea.classList.add('dragover');
    });

    videoUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        videoUploadArea.classList.remove('dragover');
    });

    videoUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        videoUploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        handleVideoFiles(files);
    });

    // 文件选择
    videoFileInput.addEventListener('change', function(e) {
        handleVideoFiles(e.target.files);
    });

    // 处理视频文件上传
    function handleVideoFiles(files) {
        if (files.length === 0) return;

        // 验证文件
        const validFiles = [];
        const invalidFiles = [];
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileType = file.type.toLowerCase();
            const fileSize = file.size;
            
            // 检查文件类型
            if (!fileType.startsWith('video/')) {
                invalidFiles.push(`${file.name}: 不是视频文件`);
                continue;
            }
            
            // 检查文件大小 (50MB - 降低限制以避免上传超时)
            if (fileSize > 50 * 1024 * 1024) {
                invalidFiles.push(`${file.name}: 文件过大 (超过50MB)`);
                continue;
            }
            
            validFiles.push(file);
        }
        
        // 显示无效文件警告
        if (invalidFiles.length > 0) {
            showError('以下文件无法上传：\n' + invalidFiles.join('\n'));
        }
        
        if (validFiles.length === 0) {
            return;
        }

        const formData = new FormData();
        for (let i = 0; i < validFiles.length; i++) {
            formData.append('video', validFiles[i]);
        }

        videoUploadProgress.style.display = 'block';
        const progressBar = videoUploadProgress.querySelector('.progress-bar');
        const progressText = videoUploadProgress.querySelector('small');
        progressText.textContent = `正在上传 ${validFiles.length} 个视频文件...`;

        fetch('/api/upload_video', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(result => {
            videoUploadProgress.style.display = 'none';
            if (result.success) {
                let message = result.message;
                if (result.failed_files && result.failed_files.length > 0) {
                    message += '\n失败的文件：\n' + result.failed_files.map(f => `${f.filename}: ${f.error}`).join('\n');
                }
                showSuccess(message);
                setTimeout(() => location.reload(), 1500);
            } else {
                let errorMessage = result.message;
                if (result.failed_files && result.failed_files.length > 0) {
                    errorMessage += '\n详细错误：\n' + result.failed_files.map(f => `${f.filename}: ${f.error}`).join('\n');
                }
                showError(errorMessage);
            }
        })
        .catch(error => {
            videoUploadProgress.style.display = 'none';
            console.error('Upload error:', error);
            showError('上传失败：' + error.message);
        });
        
        // 重置文件输入
        videoFileInput.value = '';
    }

    // 切换视图
    function toggleVideoView(viewType) {
        const gridView = document.getElementById('gridVideoView');
        const listView = document.getElementById('listVideoView');
        const gridBtn = document.getElementById('gridVideoViewBtn');
        const listBtn = document.getElementById('listVideoViewBtn');

        if (viewType === 'grid') {
            gridView.style.display = 'block';
            listView.style.display = 'none';
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        } else {
            gridView.style.display = 'none';
            listView.style.display = 'block';
            gridBtn.classList.remove('active');
            listBtn.classList.add('active');
        }
    }

    // 复制视频路径
    function copyVideoPath(path) {
        navigator.clipboard.writeText(path).then(function() {
            showSuccess('视频路径已复制到剪贴板！');
        }).catch(function(err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = path;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showSuccess('视频路径已复制到剪贴板！');
        });
    }

    // 删除视频
    function deleteVideo(filename) {
        if (confirm('确定要删除这个视频吗？删除后无法恢复！')) {
            fetch('/api/delete_video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({filename: filename})
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccess('视频删除成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showError('删除失败：' + result.message);
                }
            })
            .catch(error => {
                showError('删除失败：' + error);
            });
        }
    }

    // 预览视频
    function previewVideo(path, filename) {
        document.getElementById('previewVideo').querySelector('source').src = path;
        document.getElementById('previewVideo').load();
        document.getElementById('previewVideoModalTitle').textContent = filename;
        new bootstrap.Modal(document.getElementById('videoPreviewModal')).show();
    }

    // 刷新视频配置
    function refreshVideoConfig() {
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;

        // 显示加载状态
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
        btn.disabled = true;

        fetch('/api/refresh_videos', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess('视频配置已刷新！小程序将显示最新的视频文件。');
            } else {
                showError('刷新失败：' + result.message);
            }
        })
        .catch(error => {
            showError('刷新失败：' + error);
        })
        .finally(() => {
            // 恢复按钮状态
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 默认显示列表视图
        toggleVideoView('list');
    });
</script>

<style>
    .video-card {
        transition: transform 0.3s ease;
        overflow: hidden;
    }

    .video-card:hover {
        transform: translateY(-5px);
    }

    .video-container {
        position: relative;
        overflow: hidden;
        height: 200px;
    }

    .video-container video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .video-container:hover .video-overlay {
        opacity: 1;
    }

    .video-preview {
        width: 180px;
        height: 100px;
        object-fit: cover;
        border-radius: 4px;
    }

    .upload-area.dragover {
        border-color: var(--bs-primary);
        background-color: rgba(var(--bs-primary-rgb), 0.1);
    }
</style>
{% endblock %}
