/* pages/product-detail/product-detail.wxss */

.container {
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid var(--primary-orange);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--text-gray);
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 商品图片 */
.product-images {
  position: relative;
  height: 750rpx;
  background: #fff;
}

.image-swiper {
  width: 100%;
  height: 100%;
}

.product-image {
  width: 100%;
  height: 100%;
}

.image-indicator {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 商品信息 */
.product-info {
  padding: var(--spacing-lg);
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-md);
}

.current-price {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-orange);
  margin-right: var(--spacing-md);
}

.original-price {
  font-size: 28rpx;
  color: var(--text-gray);
  text-decoration: line-through;
}

.product-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
}

.product-subtitle {
  font-size: 28rpx;
  color: var(--text-gray);
  line-height: 1.5;
  margin-bottom: var(--spacing-lg);
}

.product-stats {
  display: flex;
  gap: var(--spacing-xl);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-gray);
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-dark);
}

.stat-unit {
  font-size: 24rpx;
  color: #FFD700;
}

/* 数量选择 */
.quantity-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
}

.selector-label {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-dark);
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.quantity-btn.disabled {
  color: var(--text-light);
  background: #f0f0f0;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  border-left: 2rpx solid var(--border-light);
  border-right: 2rpx solid var(--border-light);
  font-size: 28rpx;
}

/* 详情标签页 */
.detail-tabs {
  padding: 0;
}

.tab-headers {
  display: flex;
  border-bottom: 2rpx solid var(--border-light);
}

.tab-header {
  flex: 1;
  padding: var(--spacing-lg);
  text-align: center;
  font-size: 30rpx;
  color: var(--text-gray);
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.tab-header.active {
  color: var(--primary-orange);
  border-bottom-color: var(--primary-orange);
  font-weight: 600;
}

.tab-content {
  padding: var(--spacing-lg);
  min-height: 400rpx;
}

/* 商品详情内容 */
.product-story {
  margin-bottom: var(--spacing-xl);
}

.story-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
}

.story-content {
  font-size: 28rpx;
  color: var(--text-gray);
  line-height: 1.6;
}

.product-features {
  margin-bottom: var(--spacing-xl);
}

.features-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.feature-icon {
  color: var(--primary-orange);
  font-weight: bold;
}

.feature-text {
  font-size: 28rpx;
  color: var(--text-gray);
}

/* 规格参数 */
.specs-table {
  display: flex;
  flex-direction: column;
}

.spec-row {
  display: flex;
  padding: var(--spacing-md) 0;
  border-bottom: 1rpx solid var(--border-light);
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-label {
  width: 150rpx;
  font-size: 28rpx;
  color: var(--text-gray);
  flex-shrink: 0;
}

.spec-value {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-dark);
}

/* 用户评价 */
.reviews-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xl) 0;
  border-bottom: 1rpx solid var(--border-light);
  margin-bottom: var(--spacing-lg);
}

.rating-score {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-orange);
  margin-bottom: var(--spacing-xs);
}

.rating-stars {
  color: #FFD700;
  font-size: 32rpx;
  margin-bottom: var(--spacing-xs);
}

.rating-count {
  font-size: 24rpx;
  color: var(--text-gray);
}

.reviews-placeholder {
  text-align: center;
  padding: var(--spacing-xl) 0;
}

.placeholder-text {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 相关推荐 */
.related-products {
  padding: var(--spacing-lg);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-lg);
}

.related-scroll {
  width: 100%;
}

.related-list {
  display: flex;
  gap: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
}

.related-item {
  flex-shrink: 0;
  width: 200rpx;
}

.related-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: var(--radius-small);
  margin-bottom: var(--spacing-sm);
}

.related-name {
  font-size: 24rpx;
  color: var(--text-dark);
  line-height: 1.3;
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 26rpx;
  font-weight: 600;
  color: var(--primary-orange);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid var(--border-light);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  z-index: 100;
}

.action-left {
  display: flex;
  gap: var(--spacing-md);
}

.action-btn {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: transparent;
  border: none;
  padding: var(--spacing-xs);
  min-width: 80rpx;
}

.action-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 20rpx;
  color: var(--text-gray);
}

.action-btn.favorite.active .action-text {
  color: var(--primary-orange);
}

.cart-badge {
  position: absolute;
  top: -4rpx;
  right: 8rpx;
  background: var(--primary-orange);
  color: white;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 20rpx;
  min-width: 24rpx;
  text-align: center;
}

.action-right {
  flex: 1;
  display: flex;
  gap: var(--spacing-sm);
}

.buy-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.buy-btn.add-cart {
  background: var(--cream-white);
  color: var(--primary-orange);
  border: 2rpx solid var(--primary-orange);
}

.buy-btn.buy-now {
  background: var(--primary-orange);
  color: white;
}
