<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态文件测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-image {
            max-width: 200px;
            max-height: 150px;
            border: 2px solid #ddd;
            margin: 10px;
        }
        .status-ok { color: green; }
        .status-error { color: red; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>静态文件服务测试</h1>
        <p class="text-muted">测试所有图片和视频文件是否可以正常访问</p>
        
        <div class="row">
            <div class="col-md-8">
                <h3>图片文件测试</h3>
                <div class="row" id="imageTests">
                    <!-- 图片测试将在这里动态生成 -->
                </div>
                
                <h3 class="mt-4">视频文件测试</h3>
                <div class="row" id="videoTests">
                    <!-- 视频测试将在这里动态生成 -->
                </div>
            </div>
            
            <div class="col-md-4">
                <h3>测试结果</h3>
                <div class="card">
                    <div class="card-body">
                        <div id="testResults">
                            <p>正在测试...</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="runTests()">重新测试</button>
                    <a href="/" class="btn btn-secondary">返回首页</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        const imageFiles = [
            'avatar.jpg',
            'culture-bg.jpg', 
            'guide-cover.jpg',
            'product1.jpg',
            'product2.jpg',
            'product3.jpg',
            'product4.jpg',
            'video1.jpg',
            'video2.jpg',
            'video3.jpg'
        ];
        
        const videoFiles = [
            // 这里可以添加视频文件名
        ];
        
        let testResults = {
            images: {},
            videos: {},
            total: 0,
            passed: 0,
            failed: 0
        };

        function createImageTest(filename) {
            const url = `http://127.0.0.1:5000/images/${filename}`;
            const div = document.createElement('div');
            div.className = 'col-md-6 mb-3';
            div.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <h6>${filename}</h6>
                        <img src="${url}" class="test-image" alt="${filename}" 
                             onload="onImageLoad('${filename}')" 
                             onerror="onImageError('${filename}')">
                        <div class="mt-2">
                            <span id="status-${filename}" class="badge bg-secondary">测试中...</span>
                        </div>
                        <div class="mt-1">
                            <small><a href="${url}" target="_blank">直接访问</a></small>
                        </div>
                    </div>
                </div>
            `;
            return div;
        }

        function onImageLoad(filename) {
            testResults.images[filename] = true;
            testResults.passed++;
            document.getElementById(`status-${filename}`).className = 'badge bg-success';
            document.getElementById(`status-${filename}`).textContent = '✓ 成功';
            updateResults();
        }

        function onImageError(filename) {
            testResults.images[filename] = false;
            testResults.failed++;
            document.getElementById(`status-${filename}`).className = 'badge bg-danger';
            document.getElementById(`status-${filename}`).textContent = '✗ 失败';
            updateResults();
        }

        function updateResults() {
            const resultsDiv = document.getElementById('testResults');
            const total = testResults.passed + testResults.failed;
            
            let html = `
                <h5>测试统计</h5>
                <p><strong>总计:</strong> ${testResults.total}</p>
                <p><strong>已测试:</strong> ${total}</p>
                <p class="status-ok"><strong>成功:</strong> ${testResults.passed}</p>
                <p class="status-error"><strong>失败:</strong> ${testResults.failed}</p>
            `;
            
            if (total === testResults.total) {
                if (testResults.failed === 0) {
                    html += '<div class="alert alert-success">🎉 所有文件测试通过！</div>';
                } else {
                    html += '<div class="alert alert-warning">⚠️ 部分文件无法访问</div>';
                }
                
                html += '<h6>详细结果:</h6><ul class="list-unstyled">';
                for (const [file, success] of Object.entries(testResults.images)) {
                    const status = success ? '✓' : '✗';
                    const className = success ? 'status-ok' : 'status-error';
                    html += `<li class="${className}">${status} ${file}</li>`;
                }
                html += '</ul>';
            }
            
            resultsDiv.innerHTML = html;
        }

        function runTests() {
            // 重置结果
            testResults = {
                images: {},
                videos: {},
                total: imageFiles.length + videoFiles.length,
                passed: 0,
                failed: 0
            };
            
            // 清空容器
            document.getElementById('imageTests').innerHTML = '';
            document.getElementById('videoTests').innerHTML = '';
            
            // 创建图片测试
            const imageContainer = document.getElementById('imageTests');
            imageFiles.forEach(filename => {
                imageContainer.appendChild(createImageTest(filename));
            });
            
            updateResults();
        }

        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
