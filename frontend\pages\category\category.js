// pages/category/category.js
const api = require('../../utils/api.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    categoryList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadCategoryData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时静默刷新数据
    this.refreshCategoryDataSilently();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshCategoryData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '漆器商品分类',
      path: '/pages/category/category'
    };
  },

  /**
   * 加载分类数据
   */
  async loadCategoryData() {
    this.setData({ loading: true });

    try {
      // 检查网络状态
      const networkStatus = await api.checkNetworkStatus();
      if (!networkStatus.isConnected) {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
        this.loadDefaultCategoryData();
        this.setData({ loading: false });
        return;
      }

      // 调用API获取分类数据
      const response = await api.get('/api/miniprogram/category');

      if (response.success && response.data) {
        const categoryData = response.data;

        this.setData({
          categoryList: categoryData.categories || [],
          loading: false
        });

        console.log('分类数据加载成功:', categoryData);
      } else {
        throw new Error(response.message || '数据格式错误');
      }
    } catch (error) {
      console.error('加载分类数据失败:', error);

      // 显示错误提示
      api.handleError(error, '加载分类数据失败');

      // 加载失败时使用默认数据
      this.loadDefaultCategoryData();

      this.setData({ loading: false });
    }
  },

  /**
   * 刷新分类数据
   */
  async refreshCategoryData() {
    try {
      await this.loadCategoryData();

      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      wx.stopPullDownRefresh();
      api.handleError(error, '刷新失败');
    }
  },

  /**
   * 静默刷新分类数据（不显示loading和提示）
   */
  async refreshCategoryDataSilently() {
    try {
      // 检查网络状态
      const networkStatus = await api.checkNetworkStatus();
      if (!networkStatus.isConnected) {
        return; // 网络不可用时不刷新
      }

      // 调用API获取最新数据
      const response = await api.get('/api/miniprogram/category', {}, {
        showLoading: false // 不显示loading
      });

      if (response.success && response.data) {
        const categoryData = response.data;

        // 静默更新数据
        this.setData({
          categoryList: categoryData.categories || this.data.categoryList
        });

        console.log('分类数据静默刷新成功');
      }
    } catch (error) {
      // 静默刷新失败时不显示错误提示，只在控制台记录
      console.warn('分类数据静默刷新失败:', error);
    }
  },

  /**
   * 加载默认分类数据（当API调用失败时使用）
   */
  loadDefaultCategoryData() {
    this.setData({
      categoryList: [
        { id: 1, name: '漆扇', count: 12, category: 'fan' },
        { id: 2, name: '手镯盒', count: 8, category: 'box' },
        { id: 3, name: '笔筒', count: 15, category: 'pen' },
        { id: 4, name: '茶具', count: 20, category: 'tea' },
        { id: 5, name: '首饰盒', count: 10, category: 'jewelry' },
        { id: 6, name: '装饰品', count: 25, category: 'decoration' }
      ]
    });
  },

  /**
   * 分类点击事件
   */
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category;
    wx.showToast({
      title: `查看${category.name}商品`,
      icon: 'none'
    });
  }
})