/* app.wxss - 全局样式 */

/* 设计系统变量 */
page {
  /* 主色调 */
  --primary-orange: #FF8C42;
  --primary-orange-light: #FFB366;
  --primary-orange-dark: #E67A3A;
  
  /* 辅助色 */
  --cream-white: #FFF8F0;
  --cream-light: #FFFCF7;
  --text-dark: #2C2C2C;
  --text-gray: #666666;
  --text-light: #999999;
  --border-color: #E8E8E8;
  --shadow-light: rgba(0, 0, 0, 0.05);
  --shadow-medium: rgba(0, 0, 0, 0.1);
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  
  /* 圆角 */
  --radius-small: 8rpx;
  --radius-medium: 12rpx;
  --radius-large: 16rpx;
  --radius-xl: 20rpx;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  
  /* 阴影 */
  --shadow-card: 0 4rpx 24rpx var(--shadow-light);
  --shadow-elevated: 0 8rpx 40rpx var(--shadow-medium);
}

/* 基础重置 */
page {
  font-family: var(--font-family);
  background-color: var(--cream-white);
  color: var(--text-dark);
  line-height: 1.6;
  font-size: 28rpx;
}

/* 通用容器 */
.container {
  padding: var(--spacing-md);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.card-content {
  padding: var(--spacing-md);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-medium);
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.btn-primary {
  background: var(--primary-orange);
  color: white;
}

.btn-primary:active {
  background: var(--primary-orange-dark);
  transform: scale(0.98);
}

.btn-secondary {
  background: white;
  color: var(--primary-orange);
  border: 2rpx solid var(--primary-orange);
}

.btn-secondary:active {
  background: var(--cream-light);
}

.btn-ghost {
  background: transparent;
  color: var(--text-gray);
  border: 2rpx solid var(--border-color);
}

.btn-ghost:active {
  background: var(--cream-white);
}

.btn-small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 24rpx;
}

.btn-large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  background: var(--text-light) !important;
  color: white !important;
  cursor: not-allowed !important;
}

.btn-disabled:active {
  transform: none !important;
}

/* 文字样式 */
.text-primary {
  color: var(--primary-orange);
}

.text-secondary {
  color: var(--text-gray);
}

.text-light {
  color: var(--text-light);
}

.text-dark {
  color: var(--text-dark);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: 600;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
}

.subtitle {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: var(--spacing-sm);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

/* 间距样式 */
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }

.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }

.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }

.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }

/* 圆角样式 */
.radius-small { border-radius: var(--radius-small); }
.radius-medium { border-radius: var(--radius-medium); }
.radius-large { border-radius: var(--radius-large); }
.radius-xl { border-radius: var(--radius-xl); }
.radius-round { border-radius: 50%; }

/* 阴影样式 */
.shadow-card { box-shadow: var(--shadow-card); }
.shadow-elevated { box-shadow: var(--shadow-elevated); }

/* 边框样式 */
.border { border: 2rpx solid var(--border-color); }
.border-top { border-top: 2rpx solid var(--border-color); }
.border-bottom { border-bottom: 2rpx solid var(--border-color); }
.border-left { border-left: 2rpx solid var(--border-color); }
.border-right { border-right: 2rpx solid var(--border-color); }

/* 背景样式 */
.bg-white { background-color: white; }
.bg-cream { background-color: var(--cream-white); }
.bg-primary { background-color: var(--primary-orange); }

/* 隐藏/显示 */
.hidden { display: none !important; }
.visible { display: block !important; }

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-orange);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-gray);
  margin-bottom: var(--spacing-lg);
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  border-radius: 16rpx;
  font-weight: 500;
}

.badge-dot {
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  padding: 0;
  min-width: auto;
}
