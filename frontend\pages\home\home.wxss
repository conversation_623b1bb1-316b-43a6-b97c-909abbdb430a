/* pages/home/<USER>/

/* 首页容器 */
.home-container {
  padding: var(--spacing-md);
  background-color: var(--cream-white);
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary-orange), var(--primary-orange-light));
  -webkit-background-clip: text;
  color: transparent;
  line-height: 1.4;
}

/* 指南轮播区块 */
.guide-section {
  margin-bottom: var(--spacing-xl);
}

.guide-swiper {
  height: 360rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
}

.guide-slide {
  height: 100%;
}

/* 指南卡片 */
.guide-card {
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border-radius: 24rpx;
}

.guide-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
  z-index: 2;
}

.guide-card:active {
  transform: scale(0.98);
}

.guide-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.guide-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-lg);
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  z-index: 2;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: var(--spacing-sm);
  display: block;
}

.guide-description {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-sm);
  display: block;
  line-height: 1.4;
}

.guide-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expert-name {
  color: var(--primary-orange);
  font-weight: 500;
  font-size: 26rpx;
}

.publish-date {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 快捷入口 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.action-card {
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: center;
  transition: all 0.3s ease;
}

.action-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 12rpx var(--shadow-light);
}

.action-icon {
  font-size: 64rpx;
  margin-bottom: var(--spacing-sm);
  display: block;
}

.action-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-dark);
}

/* 视频轮播区块 */
.video-section {
  margin-bottom: var(--spacing-xl);
}

.video-swiper {
  height: 380rpx;
  border-radius: var(--radius-medium);
}

.video-slide {
  padding-right: var(--spacing-md);
}

.video-card {
  height: 100%;
  overflow: hidden;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  transition: all 0.3s ease;
}

.video-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.video-container {
  position: relative;
  width: 100%;
  height: 240rpx;
  overflow: hidden;
  border-radius: 24rpx 24rpx 0 0;
}

.video-container video {
  width: 100%;
  height: 100%;
  border-radius: 24rpx 24rpx 0 0;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
}

.video-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 1;
}

.video-thumbnail image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.video-thumbnail:active image {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 88rpx;
  height: 88rpx;
  background: linear-gradient(135deg, rgba(255, 140, 66, 0.95) 0%, rgba(255, 120, 46, 0.95) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
  box-shadow: 0 8rpx 24rpx rgba(255, 140, 66, 0.3);
}

.play-button:active {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 12rpx 32rpx rgba(255, 140, 66, 0.4);
}

.play-icon {
  color: white;
  font-size: 36rpx;
  margin-left: 6rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.video-duration-badge {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  z-index: 2;
  backdrop-filter: blur(8rpx);
}

.video-info {
  padding: 24rpx;
  background: white;
}

.video-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.4;
  display: block;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-duration {
  font-size: 28rpx;
  color: var(--text-gray);
  font-weight: 500;
}

.video-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.control-btn {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--primary-orange);
  padding: 12rpx 20rpx;
  border: 2rpx solid var(--primary-orange);
  border-radius: 20rpx;
  background: transparent;
  transition: all 0.3s ease;
  min-width: 80rpx;
  text-align: center;
}

.control-btn:active {
  background: var(--primary-orange);
  color: white;
  transform: scale(0.95);
}

/* 时间线样式 */
.timeline-section {
  margin-bottom: var(--spacing-xl);
}

.timeline {
  position: relative;
  padding-left: var(--spacing-lg);
}

.timeline::before {
  content: '';
  position: absolute;
  left: 16rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(to bottom, var(--primary-orange), var(--primary-orange-light));
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -24rpx;
  top: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: var(--primary-orange);
  border-radius: 50%;
  border: 6rpx solid white;
  box-shadow: 0 0 0 4rpx var(--primary-orange-light);
}

.timeline-dot.active {
  background: var(--primary-orange-dark);
  box-shadow: 0 0 0 4rpx var(--primary-orange), 0 0 0 8rpx var(--primary-orange-light);
}

.timeline-content {
  padding: var(--spacing-md);
}

.timeline-year {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary-orange);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.timeline-desc {
  font-size: 28rpx;
  color: var(--text-gray);
  line-height: 1.5;
}

/* 推荐商品区块 */
.recommend-section {
  margin-bottom: var(--spacing-xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.view-more {
  font-size: 28rpx;
  color: var(--primary-orange);
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-list {
  display: inline-flex;
  gap: var(--spacing-md);
  padding-right: var(--spacing-md);
}

.product-card {
  width: 280rpx;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 200rpx;
}

.product-info {
  padding: var(--spacing-md);
}

.product-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary-orange);
}

.add-cart-btn {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  width: 60rpx;
  height: 60rpx;
  background: var(--primary-orange);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-cart-btn:active {
  background: var(--primary-orange-dark);
  transform: scale(0.9);
}

.add-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 文化介绍区块 */
.culture-section {
  margin-bottom: var(--spacing-xl);
}

.culture-swiper {
  height: 320rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.culture-slide {
  height: 100%;
}

.culture-card {
  position: relative;
  height: 100%;
  overflow: hidden;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.culture-card:active {
  transform: scale(0.98);
}

.culture-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.culture-card:active .culture-image {
  transform: scale(1.05);
}

.culture-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  padding: 32rpx 24rpx 24rpx;
  color: white;
}

.culture-title {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.culture-desc {
  font-size: 28rpx;
  opacity: 0.95;
  margin-bottom: 12rpx;
  display: block;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.culture-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  opacity: 0.8;
}

.culture-author,
.culture-date {
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--text-gray);
  font-size: 28rpx;
}

/* 时间线样式 */
.timeline-section {
  margin-bottom: var(--spacing-xl);
}

.timeline {
  position: relative;
  padding-left: var(--spacing-lg);
}

.timeline::before {
  content: '';
  position: absolute;
  left: 16rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(to bottom, var(--primary-orange), var(--primary-orange-light));
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -24rpx;
  top: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: var(--primary-orange);
  border-radius: 50%;
  border: 6rpx solid white;
  box-shadow: 0 0 0 4rpx var(--primary-orange-light);
}

.timeline-dot.active {
  background: var(--primary-orange-dark);
  box-shadow: 0 0 0 4rpx var(--primary-orange), 0 0 0 8rpx var(--primary-orange-light);
}

.timeline-content {
  padding: var(--spacing-md);
}

.timeline-year {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary-orange);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.timeline-desc {
  font-size: 28rpx;
  color: var(--text-gray);
  line-height: 1.5;
}

/* 推荐商品区块 */
.recommend-section {
  margin-bottom: var(--spacing-xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.view-more {
  font-size: 28rpx;
  color: var(--primary-orange);
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-list {
  display: inline-flex;
  gap: var(--spacing-md);
  padding-right: var(--spacing-md);
}

.product-card {
  width: 280rpx;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 200rpx;
}

.product-info {
  padding: var(--spacing-md);
}

.product-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--primary-orange);
}

.add-cart-btn {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  width: 60rpx;
  height: 60rpx;
  background: var(--primary-orange);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-cart-btn:active {
  background: var(--primary-orange-dark);
  transform: scale(0.9);
}

.add-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 文化介绍区块 */
.culture-section {
  margin-bottom: var(--spacing-xl);
}

.culture-card {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.culture-image {
  width: 100%;
  height: 100%;
}

.culture-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: var(--spacing-xl) var(--spacing-md) var(--spacing-md);
  color: white;
}

.culture-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  display: block;
}

.culture-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--text-gray);
  font-size: 28rpx;
}