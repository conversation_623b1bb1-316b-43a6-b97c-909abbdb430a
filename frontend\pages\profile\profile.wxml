<!-- pages/profile/profile.wxml -->
<view class="profile-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的</text>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-card card">
    <view class="user-avatar">
      <image src="{{userInfo.avatarUrl}}" mode="aspectFill" />
    </view>
    <view class="user-info">
      <text class="user-name">{{userInfo.nickName}}</text>
      <text class="user-phone">{{userInfo.phone}}</text>
      <view class="user-level">
        <text class="level-text">{{userInfo.level}}会员</text>
        <text class="points-text">积分: {{userInfo.points}}</text>
      </view>
    </view>
    <button class="edit-btn" bindtap="onEditProfile">编辑</button>
  </view>

  <!-- 资产概览 -->
  <view class="assets-overview card">
    <view class="asset-item" bindtap="onAssetTap" data-type="coupons">
      <text class="asset-value">{{assets.coupons}}</text>
      <text class="asset-label">优惠券</text>
    </view>
    <view class="asset-item" bindtap="onAssetTap" data-type="points">
      <text class="asset-value">{{assets.points}}</text>
      <text class="asset-label">积分余额</text>
    </view>
    <view class="asset-item" bindtap="onAssetTap" data-type="balance">
      <text class="asset-value">¥{{assets.balance}}</text>
      <text class="asset-label">现金余额</text>
    </view>
    <view class="asset-item" bindtap="onAssetTap" data-type="cards">
      <text class="asset-value">{{assets.cards}}</text>
      <text class="asset-label">卡券</text>
    </view>
  </view>

  <!-- 我的订单 -->
  <view class="order-section card">
    <view class="section-header">
      <text class="section-title">我的订单</text>
      <text class="view-all" bindtap="onViewAllOrders">查看全部 →</text>
    </view>
    <view class="order-status-list">
      <view
        class="order-status-item"
        wx:for="{{orderStatusList}}"
        wx:key="type"
        bindtap="onOrderStatusTap"
        data-status="{{item.type}}"
      >
        <view class="status-icon-wrapper">
          <text class="status-icon">{{item.icon}}</text>
          <view class="status-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
        </view>
        <text class="status-text">{{item.text}}</text>
      </view>
    </view>
  </view>

  <!-- 功能入口卡片 -->
  <view class="function-cards">
    <view
      class="function-card card"
      wx:for="{{functionCards}}"
      wx:key="id"
      bindtap="onFunctionTap"
      data-function="{{item.type}}"
    >
      <text class="function-icon">{{item.icon}}</text>
      <text class="function-text">{{item.text}}</text>
    </view>
  </view>

  <!-- 独立功能入口 -->
  <view class="standalone-functions card">
    <view
      class="function-item"
      wx:for="{{standaloneFunctions}}"
      wx:key="id"
      bindtap="onStandaloneFunctionTap"
      data-function="{{item.type}}"
    >
      <view class="function-left">
        <text class="function-icon">{{item.icon}}</text>
        <text class="function-name">{{item.text}}</text>
      </view>
      <view class="function-right">
        <text class="function-count" wx:if="{{item.count}}">{{item.count}}</text>
        <text class="function-arrow">→</text>
      </view>
    </view>
  </view>
</view>