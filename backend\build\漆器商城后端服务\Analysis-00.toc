(['E:\\Desktop\\xcx\\backend\\app.py'],
 ['E:\\Desktop\\xcx\\backend'],
 [],
 [('E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('data\\miniprogram_data.json',
   'E:\\Desktop\\xcx\\backend\\data\\miniprogram_data.json',
   'DATA'),
  ('static\\css\\admin.css',
   'E:\\Desktop\\xcx\\backend\\static\\css\\admin.css',
   'DATA'),
  ('templates\\base.html',
   'E:\\Desktop\\xcx\\backend\\templates\\base.html',
   'DATA'),
  ('templates\\content.html',
   'E:\\Desktop\\xcx\\backend\\templates\\content.html',
   'DATA'),
  ('templates\\culture.html',
   'E:\\Desktop\\xcx\\backend\\templates\\culture.html',
   'DATA'),
  ('templates\\image_manager.html',
   'E:\\Desktop\\xcx\\backend\\templates\\image_manager.html',
   'DATA'),
  ('templates\\images.html',
   'E:\\Desktop\\xcx\\backend\\templates\\images.html',
   'DATA'),
  ('templates\\index.html',
   'E:\\Desktop\\xcx\\backend\\templates\\index.html',
   'DATA'),
  ('templates\\products.html',
   'E:\\Desktop\\xcx\\backend\\templates\\products.html',
   'DATA'),
  ('templates\\settings.html',
   'E:\\Desktop\\xcx\\backend\\templates\\settings.html',
   'DATA'),
  ('templates\\static_test.html',
   'E:\\Desktop\\xcx\\backend\\templates\\static_test.html',
   'DATA'),
  ('templates\\video_test.html',
   'E:\\Desktop\\xcx\\backend\\templates\\video_test.html',
   'DATA'),
  ('templates\\videos.html',
   'E:\\Desktop\\xcx\\backend\\templates\\videos.html',
   'DATA'),
  ('uploads\\images\\avatar.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\avatar.jpg',
   'DATA'),
  ('uploads\\images\\culture-bg.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture-bg.jpg',
   'DATA'),
  ('uploads\\images\\culture1-1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture1-1.jpg',
   'DATA'),
  ('uploads\\images\\culture1-2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture1-2.jpg',
   'DATA'),
  ('uploads\\images\\culture1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture1.jpg',
   'DATA'),
  ('uploads\\images\\culture2-1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture2-1.jpg',
   'DATA'),
  ('uploads\\images\\culture2-2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture2-2.jpg',
   'DATA'),
  ('uploads\\images\\culture2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture2.jpg',
   'DATA'),
  ('uploads\\images\\guide-cover.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\guide-cover.jpg',
   'DATA'),
  ('uploads\\images\\product1-2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product1-2.jpg',
   'DATA'),
  ('uploads\\images\\product1-3.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product1-3.jpg',
   'DATA'),
  ('uploads\\images\\product1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product1.jpg',
   'DATA'),
  ('uploads\\images\\product2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product2.jpg',
   'DATA'),
  ('uploads\\images\\product3.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product3.jpg',
   'DATA'),
  ('uploads\\images\\product4.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product4.jpg',
   'DATA'),
  ('uploads\\images\\video.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video.jpg',
   'DATA'),
  ('uploads\\images\\video1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video1.jpg',
   'DATA'),
  ('uploads\\images\\video2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video2.jpg',
   'DATA'),
  ('uploads\\images\\video3.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video3.jpg',
   'DATA')],
 '3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('app', 'E:\\Desktop\\xcx\\backend\\app.py', 'PYSOURCE')],
 [('pkgutil', 'D:\\applications\\miniconda\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\applications\\miniconda\\Lib\\zipimport.py', 'PYMODULE'),
  ('struct', 'D:\\applications\\miniconda\\Lib\\struct.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\applications\\miniconda\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\applications\\miniconda\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\applications\\miniconda\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\applications\\miniconda\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\applications\\miniconda\\Lib\\random.py', 'PYMODULE'),
  ('argparse', 'D:\\applications\\miniconda\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\applications\\miniconda\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\applications\\miniconda\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\applications\\miniconda\\Lib\\gettext.py', 'PYMODULE'),
  ('statistics', 'D:\\applications\\miniconda\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\applications\\miniconda\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\applications\\miniconda\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'D:\\applications\\miniconda\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions', 'D:\\applications\\miniconda\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\applications\\miniconda\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\applications\\miniconda\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\applications\\miniconda\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\applications\\miniconda\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\applications\\miniconda\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\applications\\miniconda\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\applications\\miniconda\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('threading', 'D:\\applications\\miniconda\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\applications\\miniconda\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('string', 'D:\\applications\\miniconda\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\applications\\miniconda\\Lib\\bisect.py', 'PYMODULE'),
  ('zipfile',
   'D:\\applications\\miniconda\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\applications\\miniconda\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\applications\\miniconda\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'D:\\applications\\miniconda\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\applications\\miniconda\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\applications\\miniconda\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\applications\\miniconda\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\applications\\miniconda\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\applications\\miniconda\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\applications\\miniconda\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\applications\\miniconda\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\applications\\miniconda\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'D:\\applications\\miniconda\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\applications\\miniconda\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\applications\\miniconda\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\applications\\miniconda\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\applications\\miniconda\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\applications\\miniconda\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'D:\\applications\\miniconda\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\applications\\miniconda\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\applications\\miniconda\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\applications\\miniconda\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\applications\\miniconda\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\applications\\miniconda\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\applications\\miniconda\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('socket', 'D:\\applications\\miniconda\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\applications\\miniconda\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\applications\\miniconda\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\applications\\miniconda\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\applications\\miniconda\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\applications\\miniconda\\Lib\\ipaddress.py', 'PYMODULE'),
  ('quopri', 'D:\\applications\\miniconda\\Lib\\quopri.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\applications\\miniconda\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\applications\\miniconda\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\applications\\miniconda\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\applications\\miniconda\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('email', 'D:\\applications\\miniconda\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\applications\\miniconda\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\applications\\miniconda\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('tokenize', 'D:\\applications\\miniconda\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\applications\\miniconda\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\applications\\miniconda\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\applications\\miniconda\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\applications\\miniconda\\Lib\\bz2.py', 'PYMODULE'),
  ('pathlib',
   'D:\\applications\\miniconda\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\applications\\miniconda\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('glob', 'D:\\applications\\miniconda\\Lib\\glob.py', 'PYMODULE'),
  ('fnmatch', 'D:\\applications\\miniconda\\Lib\\fnmatch.py', 'PYMODULE'),
  ('pathlib._abc',
   'D:\\applications\\miniconda\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('contextlib', 'D:\\applications\\miniconda\\Lib\\contextlib.py', 'PYMODULE'),
  ('__future__', 'D:\\applications\\miniconda\\Lib\\__future__.py', 'PYMODULE'),
  ('_strptime', 'D:\\applications\\miniconda\\Lib\\_strptime.py', 'PYMODULE'),
  ('inspect', 'D:\\applications\\miniconda\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\applications\\miniconda\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\applications\\miniconda\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata',
   'D:\\applications\\miniconda\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('ast', 'D:\\applications\\miniconda\\Lib\\ast.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\applications\\miniconda\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\applications\\miniconda\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib',
   'D:\\applications\\miniconda\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('subprocess', 'D:\\applications\\miniconda\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\applications\\miniconda\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\applications\\miniconda\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\applications\\miniconda\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\applications\\miniconda\\Lib\\gzip.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\applications\\miniconda\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\applications\\miniconda\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\applications\\miniconda\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\applications\\miniconda\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\applications\\miniconda\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\applications\\miniconda\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\applications\\miniconda\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\applications\\miniconda\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\applications\\miniconda\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\applications\\miniconda\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\applications\\miniconda\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\applications\\miniconda\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\applications\\miniconda\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\applications\\miniconda\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\applications\\miniconda\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\applications\\miniconda\\Lib\\http\\client.py',
   'PYMODULE'),
  ('hmac', 'D:\\applications\\miniconda\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\applications\\miniconda\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\applications\\miniconda\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\applications\\miniconda\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\applications\\miniconda\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\applications\\miniconda\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\applications\\miniconda\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\applications\\miniconda\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\applications\\miniconda\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\applications\\miniconda\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\applications\\miniconda\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'D:\\applications\\miniconda\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\applications\\miniconda\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\applications\\miniconda\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\applications\\miniconda\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\applications\\miniconda\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\applications\\miniconda\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\applications\\miniconda\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\applications\\miniconda\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\applications\\miniconda\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib', 'D:\\applications\\miniconda\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\applications\\miniconda\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\applications\\miniconda\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\applications\\miniconda\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\applications\\miniconda\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\applications\\miniconda\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\applications\\miniconda\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\applications\\miniconda\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\applications\\miniconda\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\applications\\miniconda\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\applications\\miniconda\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\applications\\miniconda\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\applications\\miniconda\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\applications\\miniconda\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\applications\\miniconda\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\applications\\miniconda\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\applications\\miniconda\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\applications\\miniconda\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\applications\\miniconda\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\applications\\miniconda\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\applications\\miniconda\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\applications\\miniconda\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\applications\\miniconda\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\applications\\miniconda\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\applications\\miniconda\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\applications\\miniconda\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\applications\\miniconda\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\applications\\miniconda\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\applications\\miniconda\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\applications\\miniconda\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   'D:\\applications\\miniconda\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\applications\\miniconda\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\applications\\miniconda\\Lib\\tarfile.py', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'D:\\applications\\miniconda\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support',
   'D:\\applications\\miniconda\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'D:\\applications\\miniconda\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('curses',
   'D:\\applications\\miniconda\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'D:\\applications\\miniconda\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('tty', 'D:\\applications\\miniconda\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_colorize', 'D:\\applications\\miniconda\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('code', 'D:\\applications\\miniconda\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\applications\\miniconda\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\applications\\miniconda\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\applications\\miniconda\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\applications\\miniconda\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\applications\\miniconda\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'D:\\applications\\miniconda\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\applications\\miniconda\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'D:\\applications\\miniconda\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server',
   'D:\\applications\\miniconda\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\applications\\miniconda\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html', 'D:\\applications\\miniconda\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\applications\\miniconda\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\applications\\miniconda\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\applications\\miniconda\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   'D:\\applications\\miniconda\\Lib\\configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\applications\\miniconda\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\applications\\miniconda\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\applications\\miniconda\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\applications\\miniconda\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\applications\\miniconda\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\applications\\miniconda\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\applications\\miniconda\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('uuid', 'D:\\applications\\miniconda\\Lib\\uuid.py', 'PYMODULE'),
  ('datetime', 'D:\\applications\\miniconda\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\applications\\miniconda\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('shutil', 'D:\\applications\\miniconda\\Lib\\shutil.py', 'PYMODULE'),
  ('json', 'D:\\applications\\miniconda\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\applications\\miniconda\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\applications\\miniconda\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\applications\\miniconda\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.test',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.http',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.security',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('colorama',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('markupsafe',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('flask_cors',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.version',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.core',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.templating',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.scaffold',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.wrappers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('jinja2',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.signals',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('blinker',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker.base',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('blinker._utilities',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker._saferef',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\blinker\\_saferef.py',
   'PYMODULE'),
  ('flask.helpers',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.sessions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('werkzeug.local',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.ctx',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.config',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.blueprints',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.app',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.testing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('click.testing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.core',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.shell_completion',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.parser',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.exceptions',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.types',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click._compat',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.utils',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click.termui',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.formatting',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('flask.logging',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.json.provider',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('click',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('flask.cli',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.typing',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.json',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE')],
 [('python313.dll', 'D:\\applications\\miniconda\\python313.dll', 'BINARY'),
  ('_decimal.pyd',
   'D:\\applications\\miniconda\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\applications\\miniconda\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\applications\\miniconda\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'D:\\applications\\miniconda\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\applications\\miniconda\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\applications\\miniconda\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\applications\\miniconda\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\applications\\miniconda\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\applications\\miniconda\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\applications\\miniconda\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\applications\\miniconda\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\applications\\miniconda\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\applications\\miniconda\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\applications\\miniconda\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'D:\\applications\\miniconda\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\applications\\miniconda\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\applications\\miniconda\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\applications\\miniconda\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('libmpdec-4.dll',
   'D:\\applications\\miniconda\\Library\\bin\\libmpdec-4.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\applications\\miniconda\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\applications\\miniconda\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll', 'D:\\applications\\PERL\\c\\bin\\LIBBZ2.dll', 'BINARY'),
  ('libexpat.dll',
   'D:\\applications\\miniconda\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\applications\\miniconda\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('ffi.dll', 'D:\\applications\\miniconda\\Library\\bin\\ffi.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\applications\\miniconda\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\applications\\miniconda\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\applications\\miniconda\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('data\\miniprogram_data.json',
   'E:\\Desktop\\xcx\\backend\\data\\miniprogram_data.json',
   'DATA'),
  ('static\\css\\admin.css',
   'E:\\Desktop\\xcx\\backend\\static\\css\\admin.css',
   'DATA'),
  ('templates\\base.html',
   'E:\\Desktop\\xcx\\backend\\templates\\base.html',
   'DATA'),
  ('templates\\content.html',
   'E:\\Desktop\\xcx\\backend\\templates\\content.html',
   'DATA'),
  ('templates\\culture.html',
   'E:\\Desktop\\xcx\\backend\\templates\\culture.html',
   'DATA'),
  ('templates\\image_manager.html',
   'E:\\Desktop\\xcx\\backend\\templates\\image_manager.html',
   'DATA'),
  ('templates\\images.html',
   'E:\\Desktop\\xcx\\backend\\templates\\images.html',
   'DATA'),
  ('templates\\index.html',
   'E:\\Desktop\\xcx\\backend\\templates\\index.html',
   'DATA'),
  ('templates\\products.html',
   'E:\\Desktop\\xcx\\backend\\templates\\products.html',
   'DATA'),
  ('templates\\settings.html',
   'E:\\Desktop\\xcx\\backend\\templates\\settings.html',
   'DATA'),
  ('templates\\static_test.html',
   'E:\\Desktop\\xcx\\backend\\templates\\static_test.html',
   'DATA'),
  ('templates\\video_test.html',
   'E:\\Desktop\\xcx\\backend\\templates\\video_test.html',
   'DATA'),
  ('templates\\videos.html',
   'E:\\Desktop\\xcx\\backend\\templates\\videos.html',
   'DATA'),
  ('uploads\\images\\avatar.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\avatar.jpg',
   'DATA'),
  ('uploads\\images\\culture-bg.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture-bg.jpg',
   'DATA'),
  ('uploads\\images\\culture1-1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture1-1.jpg',
   'DATA'),
  ('uploads\\images\\culture1-2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture1-2.jpg',
   'DATA'),
  ('uploads\\images\\culture1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture1.jpg',
   'DATA'),
  ('uploads\\images\\culture2-1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture2-1.jpg',
   'DATA'),
  ('uploads\\images\\culture2-2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture2-2.jpg',
   'DATA'),
  ('uploads\\images\\culture2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\culture2.jpg',
   'DATA'),
  ('uploads\\images\\guide-cover.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\guide-cover.jpg',
   'DATA'),
  ('uploads\\images\\product1-2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product1-2.jpg',
   'DATA'),
  ('uploads\\images\\product1-3.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product1-3.jpg',
   'DATA'),
  ('uploads\\images\\product1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product1.jpg',
   'DATA'),
  ('uploads\\images\\product2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product2.jpg',
   'DATA'),
  ('uploads\\images\\product3.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product3.jpg',
   'DATA'),
  ('uploads\\images\\product4.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\product4.jpg',
   'DATA'),
  ('uploads\\images\\video.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video.jpg',
   'DATA'),
  ('uploads\\images\\video1.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video1.jpg',
   'DATA'),
  ('uploads\\images\\video2.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video2.jpg',
   'DATA'),
  ('uploads\\images\\video3.jpg',
   'E:\\Desktop\\xcx\\backend\\uploads\\images\\video3.jpg',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\WHEEL',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug-2.3.7.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\INSTALLER',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug-2.3.7.dist-info\\INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\REQUESTED',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug-2.3.7.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\METADATA',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug-2.3.7.dist-info\\METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\RECORD',
   'E:\\Desktop\\xcx\\venv\\Lib\\site-packages\\werkzeug-2.3.7.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'E:\\Desktop\\xcx\\backend\\build\\漆器商城后端服务\\base_library.zip',
   'DATA')],
 [('weakref', 'D:\\applications\\miniconda\\Lib\\weakref.py', 'PYMODULE'),
  ('linecache', 'D:\\applications\\miniconda\\Lib\\linecache.py', 'PYMODULE'),
  ('io', 'D:\\applications\\miniconda\\Lib\\io.py', 'PYMODULE'),
  ('abc', 'D:\\applications\\miniconda\\Lib\\abc.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\applications\\miniconda\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\applications\\miniconda\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('locale', 'D:\\applications\\miniconda\\Lib\\locale.py', 'PYMODULE'),
  ('keyword', 'D:\\applications\\miniconda\\Lib\\keyword.py', 'PYMODULE'),
  ('codecs', 'D:\\applications\\miniconda\\Lib\\codecs.py', 'PYMODULE'),
  ('reprlib', 'D:\\applications\\miniconda\\Lib\\reprlib.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\applications\\miniconda\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('re._parser',
   'D:\\applications\\miniconda\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'D:\\applications\\miniconda\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\applications\\miniconda\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\applications\\miniconda\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('collections',
   'D:\\applications\\miniconda\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('copyreg', 'D:\\applications\\miniconda\\Lib\\copyreg.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\applications\\miniconda\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('functools', 'D:\\applications\\miniconda\\Lib\\functools.py', 'PYMODULE'),
  ('stat', 'D:\\applications\\miniconda\\Lib\\stat.py', 'PYMODULE'),
  ('genericpath',
   'D:\\applications\\miniconda\\Lib\\genericpath.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\applications\\miniconda\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\applications\\miniconda\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\applications\\miniconda\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\applications\\miniconda\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\applications\\miniconda\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\applications\\miniconda\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\applications\\miniconda\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\applications\\miniconda\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\applications\\miniconda\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\applications\\miniconda\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\applications\\miniconda\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\applications\\miniconda\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\applications\\miniconda\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\applications\\miniconda\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\applications\\miniconda\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\applications\\miniconda\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\applications\\miniconda\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\applications\\miniconda\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\applications\\miniconda\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\applications\\miniconda\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\applications\\miniconda\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\applications\\miniconda\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\applications\\miniconda\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\applications\\miniconda\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\applications\\miniconda\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\applications\\miniconda\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\applications\\miniconda\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\applications\\miniconda\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\applications\\miniconda\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\applications\\miniconda\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\applications\\miniconda\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\applications\\miniconda\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\applications\\miniconda\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\applications\\miniconda\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\applications\\miniconda\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\applications\\miniconda\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\applications\\miniconda\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\applications\\miniconda\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\applications\\miniconda\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\applications\\miniconda\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\applications\\miniconda\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\applications\\miniconda\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\applications\\miniconda\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\applications\\miniconda\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\applications\\miniconda\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('ntpath', 'D:\\applications\\miniconda\\Lib\\ntpath.py', 'PYMODULE'),
  ('posixpath', 'D:\\applications\\miniconda\\Lib\\posixpath.py', 'PYMODULE'),
  ('enum', 'D:\\applications\\miniconda\\Lib\\enum.py', 'PYMODULE'),
  ('types', 'D:\\applications\\miniconda\\Lib\\types.py', 'PYMODULE'),
  ('heapq', 'D:\\applications\\miniconda\\Lib\\heapq.py', 'PYMODULE'),
  ('traceback', 'D:\\applications\\miniconda\\Lib\\traceback.py', 'PYMODULE'),
  ('operator', 'D:\\applications\\miniconda\\Lib\\operator.py', 'PYMODULE'),
  ('sre_parse', 'D:\\applications\\miniconda\\Lib\\sre_parse.py', 'PYMODULE'),
  ('warnings', 'D:\\applications\\miniconda\\Lib\\warnings.py', 'PYMODULE'),
  ('re', 'D:\\applications\\miniconda\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('os', 'D:\\applications\\miniconda\\Lib\\os.py', 'PYMODULE')])
