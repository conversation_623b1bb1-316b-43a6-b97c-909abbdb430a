{% extends "base.html" %}

{% block title %}仪表盘 - 漆器文化商城管理后台{% endblock %}

{% block page_title %}仪表盘{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="stats-number">{{ data.category_data.categories|length }}</div>
            <div class="stats-label">商品分类</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="stats-number">{{ data.home_data.recommend_products|length }}</div>
            <div class="stats-label">推荐商品</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="stats-number">{{ data.home_data.video_list|length }}</div>
            <div class="stats-label">视频内容</div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="stats-number">{{ data.home_data.timeline_data|length }}</div>
            <div class="stats-label">历程节点</div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('content') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-edit me-2"></i>
                            编辑首页内容
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('images') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-upload me-2"></i>
                            上传图片
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>
                            导出配置
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-cog me-2"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 内容概览 -->
<div class="row">
    <!-- 首页内容概览 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-home me-2"></i>
                    首页内容概览
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>指南标题：</strong>
                    <span class="text-muted">{{ data.home_data.guide_data.title }}</span>
                </div>
                <div class="mb-3">
                    <strong>专家：</strong>
                    <span class="text-muted">{{ data.home_data.guide_data.expert_name }}</span>
                </div>
                <div class="mb-3">
                    <strong>快捷入口：</strong>
                    {% for action in data.home_data.quick_actions %}
                        <span class="badge bg-secondary me-1">{{ action.icon }} {{ action.text }}</span>
                    {% endfor %}
                </div>
                <div class="text-end">
                    <a href="{{ url_for('content') }}#home" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit me-1"></i>
                        编辑
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 商品分类概览 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-th-large me-2"></i>
                    商品分类概览
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>分类名称</th>
                                <th>商品数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in data.category_data.categories[:4] %}
                            <tr>
                                <td>{{ category.name }}</td>
                                <td><span class="badge bg-primary">{{ category.count }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-end">
                    <a href="{{ url_for('content') }}#category" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit me-1"></i>
                        管理分类
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    项目历程
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% for item in data.home_data.timeline_data %}
                    <div class="timeline-item mb-3">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <span class="badge bg-primary">{{ item.year }}</span>
                            </div>
                            <div class="col-md-10">
                                <p class="mb-0">{{ item.description }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-end mt-3">
                    <a href="{{ url_for('content') }}#timeline" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit me-1"></i>
                        编辑历程
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>应用名称：</strong> {{ data.app_config.app_name }}</p>
                        <p><strong>主题色：</strong> 
                            <span class="badge" style="background-color: {{ data.app_config.primary_color }}">
                                {{ data.app_config.primary_color }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>辅助色：</strong> 
                            <span class="badge" style="background-color: {{ data.app_config.secondary_color }}; color: #333;">
                                {{ data.app_config.secondary_color }}
                            </span>
                        </p>
                        <p><strong>最后更新：</strong> <span id="lastUpdate">刚刚</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 导出数据功能
    function exportData() {
        if (confirm('确定要导出当前配置数据吗？')) {
            // 创建下载链接
            const data = {{ data|tojson }};
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'miniprogram_config_' + new Date().toISOString().slice(0, 10) + '.json';
            link.click();
            
            showSuccess('配置数据已导出！');
        }
    }
    
    // 更新最后更新时间
    function updateLastUpdateTime() {
        const now = new Date();
        const timeStr = now.toLocaleString('zh-CN');
        document.getElementById('lastUpdate').textContent = timeStr;
    }
    
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        updateLastUpdateTime();
        
        // 每分钟更新一次时间
        setInterval(updateLastUpdateTime, 60000);
    });
</script>
{% endblock %}
