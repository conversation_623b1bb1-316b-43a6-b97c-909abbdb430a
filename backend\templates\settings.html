{% extends "base.html" %}

{% block title %}系统设置 - 漆器文化商城管理后台{% endblock %}

{% block page_title %}系统设置{% endblock %}

{% block content %}
<div class="row">
    <!-- 应用配置 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    小程序配置
                </h5>
            </div>
            <div class="card-body">
                <form id="appConfigForm">
                    <div class="mb-3">
                        <label class="form-label">应用名称</label>
                        <input type="text" class="form-control" name="app_name" 
                               value="{{ data.app_config.app_name }}">
                        <small class="form-text text-muted">显示在小程序顶部的标题</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">主题色</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" name="primary_color" 
                                   value="{{ data.app_config.primary_color }}" id="primaryColorPicker">
                            <input type="text" class="form-control" name="primary_color_text" 
                                   value="{{ data.app_config.primary_color }}" id="primaryColorText">
                        </div>
                        <small class="form-text text-muted">小程序的主要颜色，用于按钮和高亮</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">辅助色</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" name="secondary_color" 
                                   value="{{ data.app_config.secondary_color }}" id="secondaryColorPicker">
                            <input type="text" class="form-control" name="secondary_color_text" 
                                   value="{{ data.app_config.secondary_color }}" id="secondaryColorText">
                        </div>
                        <small class="form-text text-muted">小程序的辅助颜色，用于背景和卡片</small>
                    </div>
                    
                    <div class="text-end">
                        <button type="button" class="btn btn-primary" onclick="saveAppConfig()">
                            <i class="fas fa-save me-1"></i>保存配置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 数据管理 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据管理
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>导出数据</h6>
                    <p class="text-muted">导出当前所有配置数据为JSON文件</p>
                    <button class="btn btn-outline-primary" onclick="exportAllData()">
                        <i class="fas fa-download me-1"></i>导出配置数据
                    </button>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6>导入数据</h6>
                    <p class="text-muted">从JSON文件导入配置数据</p>
                    <input type="file" class="form-control" id="importFile" accept=".json">
                    <button class="btn btn-outline-success mt-2" onclick="importData()">
                        <i class="fas fa-upload me-1"></i>导入配置数据
                    </button>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6>重置数据</h6>
                    <p class="text-muted text-danger">⚠️ 这将重置所有数据到默认状态</p>
                    <button class="btn btn-outline-danger" onclick="resetAllData()">
                        <i class="fas fa-undo me-1"></i>重置到默认
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览配置 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    实时预览
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>颜色预览</h6>
                        <div class="color-preview-container">
                            <div class="color-preview-item">
                                <div class="color-box" id="primaryColorPreview" 
                                     style="background-color: {{ data.app_config.primary_color }}"></div>
                                <span>主题色</span>
                            </div>
                            <div class="color-preview-item">
                                <div class="color-box" id="secondaryColorPreview" 
                                     style="background-color: {{ data.app_config.secondary_color }}"></div>
                                <span>辅助色</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>小程序界面预览</h6>
                        <div class="miniprogram-preview">
                            <div class="preview-header" id="previewHeader">
                                <span id="previewTitle">{{ data.app_config.app_name }}</span>
                            </div>
                            <div class="preview-content">
                                <div class="preview-card">
                                    <div class="preview-button" id="previewButton">示例按钮</div>
                                </div>
                            </div>
                            <div class="preview-tabbar">
                                <div class="preview-tab active" id="previewTab">
                                    <span>首页</span>
                                </div>
                                <div class="preview-tab">分类</div>
                                <div class="preview-tab">购物车</div>
                                <div class="preview-tab">我的</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>系统版本：</strong></td>
                                <td>v1.0.0</td>
                            </tr>
                            <tr>
                                <td><strong>Flask版本：</strong></td>
                                <td>2.3.x</td>
                            </tr>
                            <tr>
                                <td><strong>Python版本：</strong></td>
                                <td>3.8+</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>数据文件：</strong></td>
                                <td><code>data/miniprogram_data.json</code></td>
                            </tr>
                            <tr>
                                <td><strong>图片目录：</strong></td>
                                <td><code>../images/</code></td>
                            </tr>
                            <tr>
                                <td><strong>最后更新：</strong></td>
                                <td id="lastUpdateTime">刚刚</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 颜色选择器同步
    document.getElementById('primaryColorPicker').addEventListener('input', function(e) {
        document.getElementById('primaryColorText').value = e.target.value;
        updatePreview();
    });

    document.getElementById('primaryColorText').addEventListener('input', function(e) {
        document.getElementById('primaryColorPicker').value = e.target.value;
        updatePreview();
    });

    document.getElementById('secondaryColorPicker').addEventListener('input', function(e) {
        document.getElementById('secondaryColorText').value = e.target.value;
        updatePreview();
    });

    document.getElementById('secondaryColorText').addEventListener('input', function(e) {
        document.getElementById('secondaryColorPicker').value = e.target.value;
        updatePreview();
    });

    // 应用名称同步
    document.querySelector('input[name="app_name"]').addEventListener('input', function(e) {
        document.getElementById('previewTitle').textContent = e.target.value;
    });

    // 更新预览
    function updatePreview() {
        const primaryColor = document.getElementById('primaryColorText').value;
        const secondaryColor = document.getElementById('secondaryColorText').value;
        
        document.getElementById('primaryColorPreview').style.backgroundColor = primaryColor;
        document.getElementById('secondaryColorPreview').style.backgroundColor = secondaryColor;
        document.getElementById('previewButton').style.backgroundColor = primaryColor;
        document.getElementById('previewTab').style.color = primaryColor;
        document.querySelector('.preview-content').style.backgroundColor = secondaryColor;
    }

    // 保存应用配置
    function saveAppConfig() {
        const formData = new FormData(document.getElementById('appConfigForm'));
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (!key.endsWith('_text')) {
                data[key] = value;
            }
        }
        
        fetch('/api/save_app_config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showSuccess('应用配置保存成功！');
            } else {
                showError('保存失败：' + result.message);
            }
        })
        .catch(error => {
            showError('保存失败：' + error);
        });
    }

    // 导出所有数据
    function exportAllData() {
        fetch('/api/export_data')
        .then(response => response.json())
        .then(data => {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'lacquerware_config_' + new Date().toISOString().slice(0, 10) + '.json';
            link.click();
            
            showSuccess('配置数据已导出！');
        })
        .catch(error => {
            showError('导出失败：' + error);
        });
    }

    // 导入数据
    function importData() {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];
        
        if (!file) {
            showError('请选择要导入的JSON文件');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                
                fetch('/api/import_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showSuccess('数据导入成功！页面将刷新...');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showError('导入失败：' + result.message);
                    }
                })
                .catch(error => {
                    showError('导入失败：' + error);
                });
            } catch (error) {
                showError('JSON文件格式错误：' + error.message);
            }
        };
        reader.readAsText(file);
    }

    // 重置所有数据
    function resetAllData() {
        if (confirm('确定要重置所有数据吗？这将删除所有自定义配置！')) {
            if (confirm('此操作不可恢复，确定继续吗？')) {
                fetch('/api/reset_data', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showSuccess('数据重置成功！页面将刷新...');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showError('重置失败：' + result.message);
                    }
                })
                .catch(error => {
                    showError('重置失败：' + error);
                });
            }
        }
    }

    // 更新时间
    function updateTime() {
        document.getElementById('lastUpdateTime').textContent = new Date().toLocaleString('zh-CN');
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        updateTime();
        setInterval(updateTime, 60000); // 每分钟更新一次
    });
</script>

<style>
    .color-preview-container {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

    .color-preview-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .color-box {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        border: 2px solid #ddd;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .miniprogram-preview {
        border: 2px solid #ddd;
        border-radius: 12px;
        overflow: hidden;
        max-width: 300px;
        margin: 0 auto;
        background: white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .preview-header {
        background: #f8f9fa;
        padding: 12px;
        text-align: center;
        border-bottom: 1px solid #ddd;
        font-weight: 600;
    }

    .preview-content {
        padding: 20px;
        min-height: 200px;
        background: #FFF8F0;
    }

    .preview-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .preview-button {
        background: #FF8C42;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        text-align: center;
        font-size: 14px;
        font-weight: 500;
    }

    .preview-tabbar {
        display: flex;
        background: white;
        border-top: 1px solid #ddd;
    }

    .preview-tab {
        flex: 1;
        padding: 10px;
        text-align: center;
        font-size: 12px;
        color: #999;
        border-right: 1px solid #eee;
    }

    .preview-tab:last-child {
        border-right: none;
    }

    .preview-tab.active {
        color: #FF8C42;
        font-weight: 600;
    }
</style>
{% endblock %}
