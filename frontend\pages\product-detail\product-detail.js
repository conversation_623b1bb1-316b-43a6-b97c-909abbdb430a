// pages/product-detail/product-detail.js
const app = getApp();
const api = require('../../utils/api.js');

Page({
  data: {
    productId: null,
    product: {},
    loading: true,
    currentImageIndex: 0,
    showImagePreview: false,
    quantity: 1,
    selectedSpecs: {},
    
    // 商品详情标签页
    activeTab: 'detail',
    
    // 评价数据
    reviews: [],
    
    // 相关推荐
    relatedProducts: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const productId = options.id;
    if (productId) {
      this.setData({ productId: parseInt(productId) });
      this.loadProductDetail();
    } else {
      wx.showToast({
        title: '商品ID无效',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新购物车数量
    this.updateCartCount();
  },

  /**
   * 加载商品详情
   */
  async loadProductDetail() {
    try {
      this.setData({ loading: true });
      
      // 获取所有商品数据
      const response = await api.get('/api/miniprogram/data');
      
      if (response.success && response.data) {
        const allData = response.data;
        const products = allData.products_data?.products || [];
        
        // 查找当前商品
        const product = products.find(p => p.id === this.data.productId);
        
        if (product) {
          // 处理商品图片
          const images = product.images || [product.image];
          
          this.setData({
            product: {
              ...product,
              images: images
            },
            loading: false
          });
          
          // 设置页面标题
          wx.setNavigationBarTitle({
            title: product.name
          });
          
          // 加载相关推荐
          this.loadRelatedProducts(products, product.category);
          
        } else {
          throw new Error('商品不存在');
        }
      } else {
        throw new Error(response.message || '获取商品详情失败');
      }
    } catch (error) {
      console.error('加载商品详情失败:', error);
      this.setData({ loading: false });
      
      wx.showModal({
        title: '加载失败',
        content: '无法获取商品详情，请稍后重试',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  /**
   * 加载相关推荐商品
   */
  loadRelatedProducts(allProducts, category) {
    const relatedProducts = allProducts
      .filter(p => p.category === category && p.id !== this.data.productId && p.status === 'active')
      .slice(0, 4);
    
    this.setData({ relatedProducts });
  },

  /**
   * 更新购物车数量
   */
  updateCartCount() {
    const cartCount = app.getCartCount();
    this.setData({
      cartCount: cartCount
    });
  },

  /**
   * 图片点击预览
   */
  onImageTap(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.product.images || [];
    
    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  /**
   * 轮播图变化
   */
  onSwiperChange(e) {
    this.setData({
      currentImageIndex: e.detail.current
    });
  },

  /**
   * 数量变化
   */
  onQuantityChange(e) {
    const type = e.currentTarget.dataset.type;
    let quantity = this.data.quantity;
    
    if (type === 'minus' && quantity > 1) {
      quantity--;
    } else if (type === 'plus' && quantity < 99) {
      quantity++;
    }
    
    this.setData({ quantity });
  },

  /**
   * 标签页切换
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  /**
   * 立即购买
   */
  onBuyNow() {
    const product = this.data.product;
    const quantity = this.data.quantity;
    
    // 检查库存
    if (product.stock < quantity) {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到订单确认页面
    wx.navigateTo({
      url: `/pages/order-confirm/order-confirm?productId=${product.id}&quantity=${quantity}`
    });
  },

  /**
   * 加入购物车
   */
  onAddToCart() {
    const product = this.data.product;
    const quantity = this.data.quantity;
    
    // 检查库存
    if (product.stock < quantity) {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
      return;
    }
    
    // 添加到购物车
    app.addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: quantity
    });
    
    wx.showToast({
      title: '已加入购物车',
      icon: 'success'
    });
    
    // 更新购物车数量
    this.updateCartCount();
  },

  /**
   * 收藏商品
   */
  onToggleFavorite() {
    const product = this.data.product;
    const isFavorite = product.isFavorite || false;
    
    // 更新收藏状态
    this.setData({
      'product.isFavorite': !isFavorite
    });
    
    wx.showToast({
      title: isFavorite ? '已取消收藏' : '已收藏',
      icon: 'success'
    });
  },

  /**
   * 分享商品
   */
  onShareProduct() {
    const product = this.data.product;
    
    return {
      title: product.name,
      path: `/pages/product-detail/product-detail?id=${product.id}`,
      imageUrl: product.image
    };
  },

  /**
   * 查看相关商品
   */
  onRelatedProductTap(e) {
    const productId = e.currentTarget.dataset.id;
    
    wx.redirectTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    });
  },

  /**
   * 联系客服
   */
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '请添加微信客服：lacquerware2024',
      showCancel: true,
      cancelText: '取消',
      confirmText: '复制微信号',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'lacquerware2024',
            success: () => {
              wx.showToast({
                title: '微信号已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadProductDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return this.onShareProduct();
  }
});
